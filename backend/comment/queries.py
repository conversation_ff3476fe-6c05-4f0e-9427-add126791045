# Comments System SQL Queries
# This file contains all the SQL queries for the comments system

# Create a new comment
CREATE_COMMENT = """
    INSERT INTO {promo_schema}.tb_comment (
        obj_id, obj_type, cell_id, text, tagged_users, created_by
    ) VALUES (
        {obj_id}, {obj_type}, {cell_id}, {text}, {tagged_users}, {created_by}
    ) RETURNING comment_id
"""

# Get comment by ID with replies
GET_COMMENT_BY_ID = """
    SELECT 
        c.comment_id,
        c.obj_id,
        c.obj_type,
        c.cell_id,
        c.text,
        c.tagged_users,
        c.status,
        c.created_by,
        c.created_at,
        c.updated_by,
        c.updated_at,
        um.name as created_by_user,
        uum.name as updated_by_user,
        r.reply_id,
        r.text as reply_text,
        r.tagged_users as reply_tagged_users,
        r.created_by as reply_created_by,
        r.created_at as reply_created_at,
        r.updated_by as reply_updated_by,
        r.updated_at as reply_updated_at,
        rum.name as reply_created_by_user,
        ruum.name as reply_updated_by_user
    FROM {promo_schema}.tb_comment c
    LEFT JOIN {global_schema}.user_master um ON um.user_code = c.created_by
    LEFT JOIN {global_schema}.user_master uum ON uum.user_code = c.updated_by
    LEFT JOIN {promo_schema}.tb_comment_reply r ON r.comment_id = c.comment_id
    LEFT JOIN {global_schema}.user_master rum ON rum.user_code = r.created_by
    LEFT JOIN {global_schema}.user_master ruum ON ruum.user_code = r.updated_by
    WHERE c.comment_id = '{comment_id}'::uuid
    ORDER BY c.created_at ASC, r.created_at ASC
"""

# Update comment
UPDATE_COMMENT = """
    UPDATE {promo_schema}.tb_comment 
    SET 
        text = {text},
        tagged_users = {tagged_users},
        updated_by = {updated_by},
        updated_at = now()
    WHERE comment_id = '{comment_id}'::uuid
    RETURNING comment_id
"""

# Update comment status
UPDATE_COMMENT_STATUS = """
    UPDATE {promo_schema}.tb_comment 
    SET 
        status = {status},
        updated_by = {updated_by},
        updated_at = now()
    WHERE comment_id = '{comment_id}'::uuid
    RETURNING comment_id
"""

# Delete comment
DELETE_COMMENT = """
    DELETE FROM {promo_schema}.tb_comment 
    WHERE comment_id = '{comment_id}'::uuid
    RETURNING comment_id
"""

# Get comments with replies (flexible query for both cell and object type)
GET_COMMENTS_WITH_REPLIES = """
    SELECT 
        c.comment_id,
        c.obj_id,
        c.obj_type,
        c.cell_id,
        c.text,
        c.tagged_users,
        c.status,
        c.created_by,
        c.created_at,
        c.updated_by,
        c.updated_at,
        um.name as created_by_user,
        uum.name as updated_by_user,
        r.reply_id,
        r.text as reply_text,
        r.tagged_users as reply_tagged_users,
        r.created_by as reply_created_by,
        r.created_at as reply_created_at,
        r.updated_by as reply_updated_by,
        r.updated_at as reply_updated_at,
        rum.name as reply_created_by_user,
        ruum.name as reply_updated_by_user
    FROM {promo_schema}.tb_comment c
    LEFT JOIN {global_schema}.user_master um ON um.user_code = c.created_by
    LEFT JOIN {global_schema}.user_master uum ON uum.user_code = c.updated_by
    LEFT JOIN {promo_schema}.tb_comment_reply r ON r.comment_id = c.comment_id
    LEFT JOIN {global_schema}.user_master rum ON rum.user_code = r.created_by
    LEFT JOIN {global_schema}.user_master ruum ON ruum.user_code = r.updated_by
    WHERE c.obj_type = {obj_type}
      {obj_id_filter}
      {obj_ids_filter}
      {cell_id_filter}
      {status_filter}
      {users_filter}
    ORDER BY c.created_at ASC, r.created_at ASC
"""


# Get comment highlights for object type (with optional obj_ids filter)
GET_COMMENT_HIGHLIGHTS = """
    SELECT 
        c.obj_id,
        c.obj_type,
        c.cell_id,
        COUNT(DISTINCT c.comment_id) as comment_count,
        COUNT(r.reply_id) as reply_count,
        COUNT(DISTINCT c.comment_id) + COUNT(r.reply_id) as total_count
    FROM {promo_schema}.tb_comment c
    LEFT JOIN {promo_schema}.tb_comment_reply r ON r.comment_id = c.comment_id
    WHERE c.obj_type = {obj_type}
      {obj_ids_filter}
    GROUP BY c.obj_id, c.obj_type, c.cell_id
    ORDER BY c.obj_id, c.cell_id
"""

# Check if comment exists and user is creator
VERIFY_COMMENT_OWNERSHIP = """
    SELECT comment_id, created_by
    FROM {promo_schema}.tb_comment
    WHERE comment_id = '{comment_id}'::uuid
"""

# =====================================================
# COMMENT REPLY QUERIES
# =====================================================

# Create a new comment reply
CREATE_COMMENT_REPLY = """
    INSERT INTO {promo_schema}.tb_comment_reply (
        comment_id, text, tagged_users, created_by
    ) VALUES (
        '{comment_id}'::uuid, {text}, {tagged_users}, {created_by}
    ) RETURNING reply_id
"""

# Get comment reply by ID
GET_COMMENT_REPLY_BY_ID = """
    SELECT 
        r.reply_id,
        r.comment_id,
        r.text,
        r.tagged_users,
        r.created_by,
        r.created_at,
        r.updated_by,
        r.updated_at,
        um.name as created_by_user,
        uum.name as updated_by_user
    FROM {promo_schema}.tb_comment_reply r
    LEFT JOIN {global_schema}.user_master um ON um.user_code = r.created_by
    LEFT JOIN {global_schema}.user_master uum ON uum.user_code = r.updated_by
    WHERE r.reply_id = '{reply_id}'::uuid
"""

# Update comment reply
UPDATE_COMMENT_REPLY = """
    UPDATE {promo_schema}.tb_comment_reply 
    SET 
        text = {text},
        tagged_users = {tagged_users},
        updated_by = {updated_by},
        updated_at = now()
    WHERE reply_id = '{reply_id}'::uuid
    RETURNING reply_id
"""

# Delete comment reply
DELETE_COMMENT_REPLY = """
    DELETE FROM {promo_schema}.tb_comment_reply 
    WHERE reply_id = '{reply_id}'::uuid
    RETURNING reply_id
"""



# Check if reply exists and user is creator
VERIFY_REPLY_OWNERSHIP = """
    SELECT reply_id, created_by
    FROM {promo_schema}.tb_comment_reply
    WHERE reply_id = '{reply_id}'::uuid
"""
