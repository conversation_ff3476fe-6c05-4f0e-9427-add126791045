-- Comments System Database Schema
-- This file contains the SQL definitions for the comments system

-- =====================================================
-- ENUM TYPES
-- =====================================================

-- Comment Status Enum
CREATE TYPE price_promo.comment_status_enum AS ENUM (
    'open',
    'resolve'
);

-- =====================================================
-- TABLES
-- =====================================================

-- Comments Table
-- Stores comments for cells in various objects
CREATE TABLE price_promo.tb_comment (
    comment_id uuid DEFAULT gen_random_uuid() NOT NULL,
    obj_id varchar(255) NOT NULL,
    obj_type varchar(100) NOT NULL,
    cell_id varchar(255) NOT NULL,
    text text NOT NULL,
    tagged_users int4[] NULL,
    status price_promo.comment_status_enum DEFAULT 'open' NOT NULL,
    created_by int4 NOT NULL,
    created_at timestamp DEFAULT now() NOT NULL,
    updated_by int4 NULL,
    updated_at timestamp NULL,
    CONSTRAINT tb_comment_pk PRIMARY KEY (comment_id),
    CONSTRAINT tb_comment_created_by_fk FOREIGN KEY (created_by) REFERENCES "global".user_master(user_code),
    CONSTRAINT tb_comment_updated_by_fk FOREIGN KEY (updated_by) REFERENCES "global".user_master(user_code)
);

-- Comment Reply Table
-- Stores replies to comments
CREATE TABLE price_promo.tb_comment_reply (
    reply_id uuid DEFAULT gen_random_uuid() NOT NULL,
    comment_id uuid NOT NULL,
    text text NOT NULL,
    tagged_users int4[] NULL,
    created_by int4 NOT NULL,
    created_at timestamp DEFAULT now() NOT NULL,
    updated_by int4 NULL,
    updated_at timestamp NULL,
    CONSTRAINT tb_comment_reply_pk PRIMARY KEY (reply_id),
    CONSTRAINT tb_comment_reply_comment_fk FOREIGN KEY (comment_id) REFERENCES price_promo.tb_comment(comment_id) ON DELETE CASCADE,
    CONSTRAINT tb_comment_reply_created_by_fk FOREIGN KEY (created_by) REFERENCES "global".user_master(user_code),
    CONSTRAINT tb_comment_reply_updated_by_fk FOREIGN KEY (updated_by) REFERENCES "global".user_master(user_code)
);

-- =====================================================
-- INDEXES
-- =====================================================

-- Comments Indexes
CREATE INDEX idx_tb_comment_obj_id ON price_promo.tb_comment USING btree (obj_id);
CREATE INDEX idx_tb_comment_obj_type ON price_promo.tb_comment USING btree (obj_type);
CREATE INDEX idx_tb_comment_cell_id ON price_promo.tb_comment USING btree (cell_id);

-- Comment Reply Indexes
CREATE INDEX idx_tb_comment_reply_comment_id ON price_promo.tb_comment_reply USING btree (comment_id);

-- =====================================================
-- PERMISSIONS
-- =====================================================

-- Grant access to "mtp-dev" role for all comment system objects

-- Grant usage on schema
GRANT USAGE ON SCHEMA price_promo TO "mtp-dev";

-- Grant access to enum types
GRANT USAGE ON TYPE price_promo.comment_status_enum TO "mtp-dev";

-- Grant access to tables
GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE price_promo.tb_comment TO "mtp-dev";
GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE price_promo.tb_comment_reply TO "mtp-dev";

-- Grant access to UUID generation function
GRANT EXECUTE ON FUNCTION gen_random_uuid() TO "mtp-dev";
