from typing import List, Optional, Dict
from pricesmart_common.utils import async_execute_query, get_str_repr, get_array_format
from comment import queries as comment_queries
from comment import models as comment_models
from configuration.environment import environment
from logger.logger import logger


def _process_comments_result(result: List[Dict]) -> List[Dict]:
    """Process and group the raw query results into structured comments with replies"""
    # Group the results by comment_id
    comments_dict = {}
    for row in result:
        comment_id = row["comment_id"]
        
        if comment_id not in comments_dict:
            comments_dict[comment_id] = {
                "comment_id": row["comment_id"],
                "obj_id": row["obj_id"],
                "obj_type": row["obj_type"],
                "cell_id": row["cell_id"],
                "text": row["text"],
                "tagged_users": row["tagged_users"],
                "status": row["status"],
                "created_by": row["created_by"],
                "created_at": row["created_at"].isoformat() if row["created_at"] else None,
                "updated_by": row["updated_by"],
                "updated_at": row["updated_at"].isoformat() if row["updated_at"] else None,
                "created_by_user": row["created_by_user"],
                "updated_by_user": row["updated_by_user"],
                "replies": []
            }
        
        # Add reply if it exists
        if row["reply_id"]:
            reply = {
                "reply_id": row["reply_id"],
                "comment_id": row["comment_id"],
                "text": row["reply_text"],
                "tagged_users": row["reply_tagged_users"],
                "created_by": row["reply_created_by"],
                "created_at": row["reply_created_at"].isoformat() if row["reply_created_at"] else None,
                "updated_by": row["reply_updated_by"],
                "updated_at": row["reply_updated_at"].isoformat() if row["reply_updated_at"] else None,
                "created_by_user": row["reply_created_by_user"],
                "updated_by_user": row["reply_updated_by_user"],
            }
            comments_dict[comment_id]["replies"].append(reply)
    
    return list(comments_dict.values())


async def create_comment(comment_request: comment_models.CommentCreate, user_id: int) -> str:
    """Create a new comment"""
    try:
        query = comment_queries.CREATE_COMMENT.format(
            promo_schema=environment.promo_schema,
            obj_id=get_str_repr(comment_request.obj_id),
            obj_type=get_str_repr(comment_request.obj_type),
            cell_id=get_str_repr(comment_request.cell_id),
            text=get_str_repr(comment_request.text),
            tagged_users=get_array_format(comment_request.tagged_users) if comment_request.tagged_users is not None else "NULL",
            created_by=user_id,
        )
        
        logger.debug(f"Creating comment for obj_id={comment_request.obj_id}, cell_id={comment_request.cell_id}")
        result = await async_execute_query(query)
        
        if result and len(result) > 0:
            comment_id = result[0]["comment_id"]
            logger.info(f"Created comment {comment_id} for user {user_id}")
            return comment_id
        else:
            raise Exception("Failed to create comment")
            
    except Exception as e:
        logger.error(f"Error in create_comment: {str(e)}")
        raise


async def get_comment_by_id(comment_id: str) -> Optional[Dict]:
    """Get a comment by ID with replies"""
    try:
        query = comment_queries.GET_COMMENT_BY_ID.format(
            promo_schema=environment.promo_schema,
            global_schema=environment.global_schema,
            comment_id=comment_id,
        )
        
        logger.debug(f"Getting comment {comment_id} with replies")
        result = await async_execute_query(query)
        
        if result and len(result) > 0:
            # Process the result to group replies with the comment
            processed_comments = _process_comments_result(result)
            if processed_comments and len(processed_comments) > 0:
                return processed_comments[0]  # Return the first (and only) comment with its replies
        return None
        
    except Exception as e:
        logger.error(f"Error in get_comment_by_id: {str(e)}")
        raise


async def update_comment(comment_id: str, text: str, tagged_users: Optional[List[int]], user_id: int) -> bool:
    """Update a comment"""
    try:
        query = comment_queries.UPDATE_COMMENT.format(
            promo_schema=environment.promo_schema,
            text=get_str_repr(text),
            tagged_users=get_array_format(tagged_users) if tagged_users is not None else "NULL",
            updated_by=user_id,
            comment_id=comment_id,
        )
        
        logger.debug(f"Updating comment {comment_id} by user {user_id}")
        result = await async_execute_query(query)
        
        if result and len(result) > 0:
            logger.info(f"Updated comment {comment_id} by user {user_id}")
            return True
        return False
        
    except Exception as e:
        logger.error(f"Error in update_comment: {str(e)}")
        raise


async def update_comment_status(comment_id: str, status: str, user_id: int) -> bool:
    """Update comment status"""
    try:
        query = comment_queries.UPDATE_COMMENT_STATUS.format(
            promo_schema=environment.promo_schema,
            status=get_str_repr(status),
            updated_by=user_id,
            comment_id=comment_id,
        )
        
        logger.debug(f"Updating comment {comment_id} status to {status} by user {user_id}")
        result = await async_execute_query(query)
        
        if result and len(result) > 0:
            logger.info(f"Updated comment {comment_id} status to {status} by user {user_id}")
            return True
        return False
        
    except Exception as e:
        logger.error(f"Error in update_comment_status: {str(e)}")
        raise


async def delete_comment(comment_id: str) -> bool:
    """Delete a comment"""
    try:
        query = comment_queries.DELETE_COMMENT.format(
            promo_schema=environment.promo_schema,
            comment_id=comment_id,
        )
        
        logger.debug(f"Deleting comment {comment_id}")
        result = await async_execute_query(query)
        
        if result and len(result) > 0:
            logger.info(f"Deleted comment {comment_id}")
            return True
        return False
        
    except Exception as e:
        logger.error(f"Error in delete_comment: {str(e)}")
        raise





async def get_comments_with_replies_by_cell(obj_id: str, obj_type: str, cell_id: str, status: Optional[str] = None, my_comments: bool = False, user_id: Optional[int] = None) -> List[Dict]:
    """Get all comments with their replies for a specific cell with optional filtering"""
    try:
        # Build filter conditions
        status_filter = ""
        if status:
            status_filter = f"AND c.status = {get_str_repr(status)}"
        
        if my_comments and user_id:
            # Filter comments by current user (created by user or tagged users include user)
            users_filter = f"AND (c.created_by = {user_id} OR c.tagged_users @> ARRAY[{user_id}]::int[] OR r.created_by = {user_id} OR r.tagged_users @> ARRAY[{user_id}]::int[])"
        else:
            users_filter = ""
        
        query = comment_queries.GET_COMMENTS_WITH_REPLIES.format(
            promo_schema=environment.promo_schema,
            global_schema=environment.global_schema,
            obj_type=get_str_repr(obj_type),
            obj_id_filter=f"AND c.obj_id = {get_str_repr(obj_id)}",
            obj_ids_filter="",  # Not used for single cell queries
            cell_id_filter=f"AND c.cell_id = {get_str_repr(cell_id)}",
            status_filter=status_filter,
            users_filter=users_filter,
        )
        
        logger.debug(f"Getting comments with replies for cell {cell_id} in {obj_type} {obj_id}")
        result = await async_execute_query(query)
        
        return _process_comments_result(result)
        
    except Exception as e:
        logger.error(f"Error in get_comments_with_replies_by_cell: {str(e)}")
        raise


async def get_comment_highlights(obj_type: str, obj_ids: Optional[List[str]] = None) -> List[Dict]:
    """Get comment highlights for an object type with optional obj_ids filter"""
    try:
        # Build filter condition
        obj_ids_filter = ""
        if obj_ids:
            obj_ids_filter = f"AND obj_id = ANY({get_array_format(obj_ids)}::text[])"
        
        query = comment_queries.GET_COMMENT_HIGHLIGHTS.format(
            promo_schema=environment.promo_schema,
            obj_type=get_str_repr(obj_type),
            obj_ids_filter=obj_ids_filter,
        )
        
        logger.debug(f"Getting comment highlights for obj_type={obj_type}, obj_count={len(obj_ids) if obj_ids else 0}")
        result = await async_execute_query(query)
        
        logger.debug(f"Retrieved {len(result) if result else 0} comment highlights")
        return result if result else []
        
    except Exception as e:
        logger.error(f"Error in get_comment_highlights: {str(e)}")
        raise


async def verify_comment_ownership(comment_id: str) -> Optional[Dict]:
    """Verify comment exists and return ownership info"""
    try:
        query = comment_queries.VERIFY_COMMENT_OWNERSHIP.format(
            promo_schema=environment.promo_schema,
            comment_id=comment_id,
        )
        
        logger.debug(f"Verifying comment ownership for {comment_id}")
        result = await async_execute_query(query)
        
        if result and len(result) > 0:
            return result[0]
        return None
        
    except Exception as e:
        logger.error(f"Error in verify_comment_ownership: {str(e)}")
        raise


# =====================================================
# COMMENT REPLY DATA FUNCTIONS
# =====================================================

async def create_comment_reply(reply_request: comment_models.CommentReplyCreate, user_id: int) -> str:
    """Create a new comment reply"""
    try:
        query = comment_queries.CREATE_COMMENT_REPLY.format(
            promo_schema=environment.promo_schema,
            comment_id=reply_request.comment_id,
            text=get_str_repr(reply_request.text),
            tagged_users=get_array_format(reply_request.tagged_users) if reply_request.tagged_users is not None else "NULL",
            created_by=user_id,
        )
        
        logger.debug(f"Creating reply for comment {reply_request.comment_id}")
        result = await async_execute_query(query)
        
        if result and len(result) > 0:
            reply_id = result[0]["reply_id"]
            logger.info(f"Created reply {reply_id} for comment {reply_request.comment_id} by user {user_id}")
            return reply_id
        else:
            raise Exception("Failed to create comment reply")
            
    except Exception as e:
        logger.error(f"Error in create_comment_reply: {str(e)}")
        raise


async def get_comment_reply_by_id(reply_id: str) -> Optional[Dict]:
    """Get a comment reply by ID"""
    try:
        query = comment_queries.GET_COMMENT_REPLY_BY_ID.format(
            promo_schema=environment.promo_schema,
            global_schema=environment.global_schema,
            reply_id=reply_id,
        )
        
        logger.debug(f"Getting reply {reply_id}")
        result = await async_execute_query(query)
        
        if result and len(result) > 0:
            return result[0]
        return None
        
    except Exception as e:
        logger.error(f"Error in get_comment_reply_by_id: {str(e)}")
        raise


async def update_comment_reply(reply_id: str, text: str, tagged_users: Optional[List[int]], user_id: int) -> bool:
    """Update a comment reply"""
    try:
        query = comment_queries.UPDATE_COMMENT_REPLY.format(
            promo_schema=environment.promo_schema,
            text=get_str_repr(text),
            tagged_users=get_array_format(tagged_users) if tagged_users is not None else "NULL",
            updated_by=user_id,
            reply_id=reply_id,
        )
        
        logger.debug(f"Updating reply {reply_id} by user {user_id}")
        result = await async_execute_query(query)
        
        if result and len(result) > 0:
            logger.info(f"Updated reply {reply_id} by user {user_id}")
            return True
        return False
        
    except Exception as e:
        logger.error(f"Error in update_comment_reply: {str(e)}")
        raise


async def delete_comment_reply(reply_id: str) -> bool:
    """Delete a comment reply"""
    try:
        query = comment_queries.DELETE_COMMENT_REPLY.format(
            promo_schema=environment.promo_schema,
            reply_id=reply_id,
        )
        
        logger.debug(f"Deleting reply {reply_id}")
        result = await async_execute_query(query)
        
        if result and len(result) > 0:
            logger.info(f"Deleted reply {reply_id}")
            return True
        return False
        
    except Exception as e:
        logger.error(f"Error in delete_comment_reply: {str(e)}")
        raise





async def verify_reply_ownership(reply_id: str) -> Optional[Dict]:
    """Verify reply exists and return ownership info"""
    try:
        query = comment_queries.VERIFY_REPLY_OWNERSHIP.format(
            promo_schema=environment.promo_schema,
            reply_id=reply_id,
        )
        
        logger.debug(f"Verifying reply ownership for {reply_id}")
        result = await async_execute_query(query)
        
        if result and len(result) > 0:
            return result[0]
        return None
        
    except Exception as e:
        logger.error(f"Error in verify_reply_ownership: {str(e)}")
        raise


async def get_comments_with_replies_by_obj_type(obj_type: str, obj_ids: Optional[List[str]] = None, status: Optional[str] = None, my_comments: bool = False, user_id: Optional[int] = None) -> List[Dict]:
    """Get all comments with their replies for a specific object type with optional filtering by obj_ids"""
    try:
        # Build filter conditions
        status_filter = ""
        if status:
            status_filter = f"AND c.status = {get_str_repr(status)}"
        
        # Build obj_ids filter
        obj_ids_filter = ""
        if obj_ids:
            obj_ids_filter = f"AND c.obj_id = ANY({get_array_format(obj_ids)}::text[])"
        
        if my_comments and user_id:
            # Filter comments by current user (created by user or tagged users include user)
            users_filter = f"AND (c.created_by = {user_id} OR c.tagged_users @> ARRAY[{user_id}]::int[] OR r.created_by = {user_id} OR r.tagged_users @> ARRAY[{user_id}]::int[])"
        else:
            users_filter = ""
        
        query = comment_queries.GET_COMMENTS_WITH_REPLIES.format(
            promo_schema=environment.promo_schema,
            global_schema=environment.global_schema,
            obj_type=get_str_repr(obj_type),
            obj_id_filter="",
            obj_ids_filter=obj_ids_filter,
            cell_id_filter="",
            status_filter=status_filter,
            users_filter=users_filter,
        )
        
        obj_count = len(obj_ids) if obj_ids else "all"
        logger.debug(f"Getting comments with replies for object type {obj_type} (obj_ids count: {obj_count})")
        result = await async_execute_query(query)
        
        return _process_comments_result(result)
        
    except Exception as e:
        logger.error(f"Error in get_comments_with_replies_by_obj_type: {str(e)}")
        raise
