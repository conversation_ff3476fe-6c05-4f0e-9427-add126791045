from typing import List, Dict
from comment import data as comment_data
from comment import models as comment_models
from comment import constants as comment_constants
from exceptions.exceptions import NotFoundException, InvalidAccessException
from logger.logger import logger


async def create_comment(comment_request: comment_models.CommentCreate, user_id: int) -> Dict:
    """Create a new comment"""
    try:
        
        # Create the comment
        comment_id = await comment_data.create_comment(comment_request, user_id)
        
        # Get the created comment details
        comment = await comment_data.get_comment_by_id(comment_id)
        
        if not comment:
            raise Exception("Failed to retrieve created comment")
        
        logger.info(f"Created comment {comment_id} for user {user_id} on {comment_request.obj_type} {comment_request.obj_id}")
        return comment
        
    except Exception as e:
        logger.error(f"Error in create_comment service: {str(e)}", extra={
            "user_id": user_id,
            "obj_type": comment_request.obj_type if hasattr(comment_request, 'obj_type') else None,
            "obj_id": comment_request.obj_id if hasattr(comment_request, 'obj_id') else None
        })
        raise


async def get_comment_by_id(comment_id: str) -> Dict:
    """Get a specific comment by ID"""
    try:
        comment = await comment_data.get_comment_by_id(comment_id)
        if not comment:
            raise NotFoundException(comment_constants.COMMENT_NOT_FOUND)
        return comment
        
    except NotFoundException:
        raise
    except Exception as e:
        logger.error(f"Error in get_comment_by_id service: {str(e)}", extra={"comment_id": comment_id})
        raise


async def update_comment(comment_id: str, comment_request: comment_models.CommentUpdate, user_id: int) -> Dict:
    """Update a comment"""
    try:
        
        # Verify comment exists and user is the creator
        ownership_info = await comment_data.verify_comment_ownership(comment_id)
        if not ownership_info:
            raise NotFoundException(comment_constants.COMMENT_NOT_FOUND)
        
        if str(ownership_info["created_by"]) != str(user_id):
            raise InvalidAccessException(comment_constants.INVALID_ACCESS)
        
        # Update the comment
        success = await comment_data.update_comment(comment_id, comment_request.text, comment_request.tagged_users, user_id)
        if not success:
            raise Exception("Failed to update comment")
        
        # Get the updated comment
        comment = await comment_data.get_comment_by_id(comment_id)
        if not comment:
            raise Exception("Failed to retrieve updated comment")
        
        logger.info(f"Updated comment {comment_id} by user {user_id}")
        return comment
        
    except Exception as e:
        logger.error(f"Error in update_comment service: {str(e)}")
        raise


async def update_comment_status(comment_id: str, status_request: comment_models.CommentStatusUpdate, user_id: int) -> Dict:
    """Update comment status"""
    try:
        
        # Verify comment exists
        ownership_info = await comment_data.verify_comment_ownership(comment_id)
        if not ownership_info:
            raise NotFoundException(comment_constants.COMMENT_NOT_FOUND)
        
        # Update the comment status
        success = await comment_data.update_comment_status(comment_id, status_request.status.value, user_id)
        if not success:
            raise Exception("Failed to update comment status")
        
        # Get the updated comment
        comment = await comment_data.get_comment_by_id(comment_id)
        if not comment:
            raise Exception("Failed to retrieve updated comment")
        
        logger.info(f"Updated comment {comment_id} status to {status_request.status.value} by user {user_id}")
        return comment
        
    except Exception as e:
        logger.error(f"Error in update_comment_status service: {str(e)}")
        raise


async def delete_comment(comment_id: str, user_id: int) -> bool:
    """Delete a comment"""
    try:
        # Verify comment exists and user is the creator
        ownership_info = await comment_data.verify_comment_ownership(comment_id)
        if not ownership_info:
            raise NotFoundException(comment_constants.COMMENT_NOT_FOUND)
        
        if str(ownership_info["created_by"]) != str(user_id):
            raise InvalidAccessException(comment_constants.INVALID_ACCESS)
        
        # Delete the comment
        success = await comment_data.delete_comment(comment_id)
        if not success:
            raise Exception("Failed to delete comment")
        
        logger.info(f"Deleted comment {comment_id} by user {user_id}")
        return True
        
    except Exception as e:
        logger.error(f"Error in delete_comment service: {str(e)}")
        raise


async def get_comments_by_cell(obj_type: str, obj_id: str, cell_id: str, status: str = None, my_comments: bool = False, user_id: int = None) -> List[Dict]:
    """Get all comments with replies for a specific cell with optional filtering"""
    try:
        comments = await comment_data.get_comments_with_replies_by_cell(obj_id, obj_type, cell_id, status, my_comments, user_id)
        logger.info(f"Retrieved {len(comments)} comments with replies for cell {cell_id} in {obj_type} {obj_id}")
        return comments
        
    except Exception as e:
        logger.error(f"Error in get_comments_by_cell service: {str(e)}")
        raise


async def get_comments_by_obj_type(obj_type: str, obj_ids: List[str] = None, status: str = None, my_comments: bool = False, user_id: int = None) -> List[Dict]:
    """Get all comments with replies for a specific object type with optional filtering by obj_ids"""
    try:
        comments = await comment_data.get_comments_with_replies_by_obj_type(obj_type, obj_ids, status, my_comments, user_id)
        obj_count = len(obj_ids) if obj_ids else "all"
        logger.info(f"Retrieved {len(comments)} comments with replies for object type {obj_type} (obj_ids count: {obj_count})")
        return comments
        
    except Exception as e:
        logger.error(f"Error in get_comments_by_obj_type service: {str(e)}")
        raise


async def get_comment_highlights(filter_data: comment_models.CommentFilter) -> List[Dict]:
    """Get comment highlights for an object type with optional obj_ids filter"""
    try:
        highlights = await comment_data.get_comment_highlights(
            filter_data.obj_type, 
            filter_data.obj_ids
        )
        
        obj_count = len(filter_data.obj_ids) if filter_data.obj_ids else "all"
        logger.info(f"Retrieved {len(highlights)} comment highlights for {filter_data.obj_type} (obj_ids count: {obj_count})")
        return highlights
        
    except Exception as e:
        logger.error(f"Error in get_comment_highlights service: {str(e)}")
        raise


# =====================================================
# COMMENT REPLY SERVICE FUNCTIONS
# =====================================================

async def create_comment_reply(reply_request: comment_models.CommentReplyCreate, user_id: int) -> Dict:
    """Create a new comment reply"""
    try:
        
        # Verify parent comment exists
        parent_comment = await comment_data.get_comment_by_id(reply_request.comment_id)
        if not parent_comment:
            raise NotFoundException("Parent comment not found")
        
        # Create the reply
        reply_id = await comment_data.create_comment_reply(reply_request, user_id)
        
        # Get the created reply details
        reply = await comment_data.get_comment_reply_by_id(reply_id)
        
        if not reply:
            raise Exception("Failed to retrieve created reply")
        
        logger.info(f"Created reply {reply_id} for comment {reply_request.comment_id} by user {user_id}")
        return reply
        
    except Exception as e:
        logger.error(f"Error in create_comment_reply service: {str(e)}", extra={
            "user_id": user_id,
            "comment_id": reply_request.comment_id if hasattr(reply_request, 'comment_id') else None
        })
        raise


async def get_comment_reply_by_id(reply_id: str) -> Dict:
    """Get a specific comment reply by ID"""
    try:
        reply = await comment_data.get_comment_reply_by_id(reply_id)
        if not reply:
            raise NotFoundException("Reply not found")
        return reply
        
    except NotFoundException:
        raise
    except Exception as e:
        logger.error(f"Error in get_comment_reply_by_id service: {str(e)}", extra={"reply_id": reply_id})
        raise


async def update_comment_reply(reply_id: str, reply_request: comment_models.CommentReplyUpdate, user_id: int) -> Dict:
    """Update a comment reply"""
    try:
        
        # Verify reply exists and user is the creator
        ownership_info = await comment_data.verify_reply_ownership(reply_id)
        if not ownership_info:
            raise NotFoundException("Reply not found")
        
        if str(ownership_info["created_by"]) != str(user_id):
            raise InvalidAccessException("You can only edit your own replies")
        
        # Update the reply
        success = await comment_data.update_comment_reply(reply_id, reply_request.text, reply_request.tagged_users, user_id)
        if not success:
            raise Exception("Failed to update reply")
        
        # Get the updated reply
        reply = await comment_data.get_comment_reply_by_id(reply_id)
        if not reply:
            raise Exception("Failed to retrieve updated reply")
        
        logger.info(f"Updated reply {reply_id} by user {user_id}")
        return reply
        
    except Exception as e:
        logger.error(f"Error in update_comment_reply service: {str(e)}")
        raise


async def delete_comment_reply(reply_id: str, user_id: int) -> bool:
    """Delete a comment reply"""
    try:
        # Verify reply exists and user is the creator
        ownership_info = await comment_data.verify_reply_ownership(reply_id)
        if not ownership_info:
            raise NotFoundException("Reply not found")
        
        if str(ownership_info["created_by"]) != str(user_id):
            raise InvalidAccessException("You can only delete your own replies")
        
        # Delete the reply
        success = await comment_data.delete_comment_reply(reply_id)
        if not success:
            raise Exception("Failed to delete reply")
        
        logger.info(f"Deleted reply {reply_id} by user {user_id}")
        return True
        
    except Exception as e:
        logger.error(f"Error in delete_comment_reply service: {str(e)}")
        raise



