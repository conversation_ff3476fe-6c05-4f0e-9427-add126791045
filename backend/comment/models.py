from typing import List, Optional
from pydantic import BaseModel, Field, field_validator
from enum import Enum
from exceptions.exceptions import CommonException


class CommentStatusEnum(str, Enum):
    OPEN = "open"
    RESOLVE = "resolve"


class CommentCreate(BaseModel):
    obj_id: str = Field(..., description="Object ID")
    obj_type: str = Field(..., description="Object type")
    cell_id: str = Field(..., description="Cell ID")
    text: str = Field(..., description="Comment text")
    tagged_users: Optional[List[int]] = Field(default=None, description="List of tagged user IDs")

    @field_validator("text")
    @classmethod
    def validate_text(cls, value):
        if not value or not value.strip():
            raise CommonException("Comment text cannot be empty")
        return value.strip()

    @field_validator("obj_id", "obj_type", "cell_id")
    @classmethod
    def validate_required_fields(cls, value):
        if not value or not value.strip():
            raise CommonException("Required field cannot be empty")
        if len(value.strip()) > 255:  # Match database column limit
            raise CommonException("Field value cannot exceed 255 characters")
        return value.strip()


class CommentUpdate(BaseModel):
    text: str = Field(..., description="Updated comment text")
    tagged_users: Optional[List[int]] = Field(default=None, description="List of tagged user IDs")

    @field_validator("text")
    @classmethod
    def validate_text(cls, value):
        if not value or not value.strip():
            raise CommonException("Comment text cannot be empty")
        return value.strip()


class CommentStatusUpdate(BaseModel):
    status: CommentStatusEnum = Field(..., description="New comment status")


class CommentReplyCreate(BaseModel):
    comment_id: str = Field(..., description="Parent comment ID")
    text: str = Field(..., description="Reply text")
    tagged_users: Optional[List[int]] = Field(default=None, description="List of tagged user IDs")

    @field_validator("text")
    @classmethod
    def validate_text(cls, value):
        if not value or not value.strip():
            raise CommonException("Reply text cannot be empty")
        return value.strip()

    @field_validator("comment_id")
    @classmethod
    def validate_comment_id(cls, value):
        if not value or not value.strip():
            raise CommonException("Comment ID cannot be empty")
        return value.strip()


class CommentReplyUpdate(BaseModel):
    text: str = Field(..., description="Updated reply text")
    tagged_users: Optional[List[int]] = Field(default=None, description="List of tagged user IDs")

    @field_validator("text")
    @classmethod
    def validate_text(cls, value):
        if not value or not value.strip():
            raise CommonException("Reply text cannot be empty")
        return value.strip()


class CommentFilter(BaseModel):
    obj_type: str = Field(..., description="Object type")
    obj_ids: Optional[List[str]] = Field(default=None, description="Optional list of object IDs to filter highlights")

    @field_validator("obj_type")
    @classmethod
    def validate_obj_type(cls, value):
        if not value or not value.strip():
            raise CommonException("Object type cannot be empty")
        return value.strip()

    @field_validator("obj_ids")
    @classmethod
    def validate_obj_ids(cls, value):
        if value is not None:
            # Remove empty strings and duplicates
            cleaned_ids = list(set([obj_id.strip() for obj_id in value if obj_id and obj_id.strip()]))
            if not cleaned_ids:
                return None
            return cleaned_ids
        return value


class CommentListRequest(BaseModel):
    """Request payload for listing comments by object type"""
    obj_ids: Optional[List[str]] = Field(default=None, description="Optional list of object IDs to filter comments (can be large arrays)")
    status: Optional[str] = Field(default=None, description="Filter by comment status (open/resolve)")
    my_comments: bool = Field(default=False, description="Show only user's comments and replies")

    @field_validator("obj_ids")
    @classmethod
    def validate_obj_ids(cls, value):
        if value is not None:
            # Remove empty strings and duplicates, preserve order for large lists
            seen = set()
            cleaned_ids = []
            for obj_id in value:
                if obj_id and obj_id.strip() and obj_id.strip() not in seen:
                    cleaned_id = obj_id.strip()
                    seen.add(cleaned_id)
                    cleaned_ids.append(cleaned_id)
            
            if not cleaned_ids:
                return None
            return cleaned_ids
        return value

    @field_validator("status")
    @classmethod
    def validate_status(cls, value):
        if value is not None:
            value = value.strip()
            if value not in ["open", "resolve"]:
                raise CommonException("Status must be 'open' or 'resolve'")
            return value
        return value
