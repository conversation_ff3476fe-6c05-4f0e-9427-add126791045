# PriceSmart Comments System

## Overview

The PriceSmart Comments System is a comprehensive commenting platform built with FastAPI, designed to provide cell-level commenting functionality with threaded replies for various business objects like promotions, events, store groups, and products. The system allows users to add, edit, delete, and view comments on specific cells within objects, with support for comment replies, status management, and user tagging.

## Table of Contents

- [High-Level Architecture](#high-level-architecture)
- [Low-Level Architecture](#low-level-architecture)
- [Database Schema](#database-schema)
- [API Endpoints](#api-endpoints)
- [User Flows](#user-flows)
- [Components Overview](#components-overview)
- [Data Models](#data-models)
- [Security & Permissions](#security--permissions)
- [Performance Considerations](#performance-considerations)
- [Error Handling](#error-handling)
- [Monitoring & Observability](#monitoring--observability)
- [Future Enhancements](#future-enhancements)
- [Integration Points](#integration-points)

## High-Level Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        WEB[Web Frontend]
        MOB[Mobile App]
    end
    
    subgraph "API Gateway"
        NGINX[NGINX/Load Balancer]
    end
    
    subgraph "Application Layer"
        FASTAPI[FastAPI Application]
        COMMENTS[Comments Module]
    end
    
    subgraph "Data Layer"
        POSTGRES[(PostgreSQL Database)]
    end
    
    subgraph "External Services"
        FIREBASE[Firebase Auth]
        SECRETS[Google Secret Manager]
    end
    
    WEB --> NGINX
    MOB --> NGINX
    NGINX --> FASTAPI
    FASTAPI --> COMMENTS
    COMMENTS --> POSTGRES
    FASTAPI --> FIREBASE
    FASTAPI --> SECRETS
```

## Low-Level Architecture

### Module Structure

```
backend/comment/
├── __init__.py
├── constants.py          # Application constants and error messages
├── controller.py         # FastAPI route handlers
├── data.py              # Database operations
├── models.py            # Pydantic models and validation
├── queries.py           # SQL queries
├── schema.sql           # Database schema
├── service.py           # Business logic
└── README.md            # This documentation
```

### Layer Architecture

```mermaid
graph TB
    subgraph "Presentation Layer"
        CTRL[Controller Layer]
    end
    
    subgraph "Business Logic Layer"
        SVC[Service Layer]
    end
    
    subgraph "Data Access Layer"
        DATA[Data Layer]
        QUERIES[Query Layer]
    end
    
    subgraph "Database Layer"
        COMMENTS[(tb_comment)]
        REPLIES[(tb_comment_reply)]
    end
    
    CTRL --> SVC
    SVC --> DATA
    DATA --> QUERIES
    QUERIES --> COMMENTS
    QUERIES --> REPLIES
```

## Database Schema

### Core Tables

#### tb_comment
Stores comment information for cells in various objects
```sql
CREATE TABLE price_promo.tb_comment (
    comment_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    obj_id varchar(255) NOT NULL,
    obj_type varchar(100) NOT NULL,
    cell_id varchar(255) NOT NULL,
    text text NOT NULL,
    tagged_users int4[] NULL,
    status price_promo.comment_status_enum DEFAULT 'open' NOT NULL,
    created_by int4 NOT NULL,
    created_at timestamp DEFAULT now() NOT NULL,
    updated_by int4 NULL,
    updated_at timestamp NULL,
    CONSTRAINT tb_comment_pk PRIMARY KEY (comment_id),
    CONSTRAINT tb_comment_created_by_fk FOREIGN KEY (created_by) REFERENCES "global".user_master(user_code),
    CONSTRAINT tb_comment_updated_by_fk FOREIGN KEY (updated_by) REFERENCES "global".user_master(user_code)
);
```

#### tb_comment_reply
Stores replies to comments with threading support
```sql
CREATE TABLE price_promo.tb_comment_reply (
    reply_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    comment_id uuid NOT NULL,
    text text NOT NULL,
    tagged_users int4[] NULL,
    created_by int4 NOT NULL,
    created_at timestamp DEFAULT now() NOT NULL,
    updated_by int4 NULL,
    updated_at timestamp NULL,
    CONSTRAINT tb_comment_reply_pk PRIMARY KEY (reply_id),
    CONSTRAINT tb_comment_reply_comment_fk FOREIGN KEY (comment_id) REFERENCES price_promo.tb_comment(comment_id) ON DELETE CASCADE,
    CONSTRAINT tb_comment_reply_created_by_fk FOREIGN KEY (created_by) REFERENCES "global".user_master(user_code),
    CONSTRAINT tb_comment_reply_updated_by_fk FOREIGN KEY (updated_by) REFERENCES "global".user_master(user_code)
);
```

### Enum Types
```sql
CREATE TYPE price_promo.comment_status_enum AS ENUM (
    'open',
    'resolve'
);
```

### Indexes
```sql
-- Performance optimization indexes
CREATE INDEX idx_tb_comment_obj_id ON price_promo.tb_comment USING btree (obj_id);
CREATE INDEX idx_tb_comment_obj_type ON price_promo.tb_comment USING btree (obj_type);
CREATE INDEX idx_tb_comment_cell_id ON price_promo.tb_comment USING btree (cell_id);
CREATE INDEX idx_tb_comment_reply_comment_id ON price_promo.tb_comment_reply USING btree (comment_id);
```

## API Endpoints

### Comment Management
- `POST /api/v3/comments/comment` - Create new comment
- `GET /api/v3/comments/comment/{comment_id}` - Get comment by ID
- `PATCH /api/v3/comments/comment/{comment_id}` - Update comment
- `DELETE /api/v3/comments/comment/{comment_id}` - Delete comment
- `PATCH /api/v3/comments/comment/{comment_id}/status` - Update comment status

### Comment Retrieval
- `GET /api/v3/comments/comment/{obj_type}/{obj_id}/{cell_id}` - Get all comments for a cell (supports query parameters: status, my_comments)
- `POST /api/v3/comments/comments/{obj_type}` - Get all comments for an object type (uses POST to handle large obj_ids arrays)
- `POST /api/v3/comments/comment/highlights` - Get comment highlights for cells

### Comment Reply Management
- `POST /api/v3/comments/comment/reply` - Create new reply to a comment
- `GET /api/v3/comments/comment/reply/{reply_id}` - Get reply by ID
- `PATCH /api/v3/comments/comment/reply/{reply_id}` - Update reply
- `DELETE /api/v3/comments/comment/reply/{reply_id}` - Delete reply

## User Flows

### 1. Comment Creation Flow

```mermaid
sequenceDiagram
    participant U as User
    participant C as Controller
    participant S as Service
    participant D as Data Layer
    
    U->>C: POST /comment (comment_data)
    C->>S: create_comment(comment_data, user_id)
    S->>D: create_comment(comment_data, user_id)
    D->>D: Insert comment with status 'open'
    D-->>S: comment_id
    S->>D: get_comment_by_id(comment_id)
    D-->>S: comment_details
    S-->>C: comment_details
    C-->>U: Response with comment data
```

### 2. Comment Reply Flow

```mermaid
sequenceDiagram
    participant U as User
    participant C as Controller
    participant S as Service
    participant D as Data Layer
    
    U->>C: POST /comment/reply (reply_data)
    C->>S: create_comment_reply(reply_data, user_id)
    S->>D: create_comment_reply(reply_data, user_id)
    D->>D: Insert reply linked to comment
    D-->>S: reply_id
    S->>D: get_comment_reply_by_id(reply_id)
    D-->>S: reply_details
    S-->>C: reply_details
    C-->>U: Response with reply data
```

### 3. Comment Status Update Flow

```mermaid
sequenceDiagram
    participant U as User
    participant C as Controller
    participant S as Service
    participant D as Data Layer
    
    U->>C: PATCH /comment/{comment_id}/status (status_data)
    C->>S: update_comment_status(comment_id, status_data, user_id)
    S->>D: verify_comment_ownership(comment_id)
    D-->>S: ownership_info
    S->>D: update_comment_status(comment_id, status, user_id)
    D-->>S: success
    S->>D: get_comment_by_id(comment_id)
    D-->>S: updated_comment
    S-->>C: updated_comment
    C-->>U: Response with updated comment
```

## Components Overview

### 1. Controller Layer (`controller.py`)
- **Responsibility**: HTTP request handling and routing
- **Key Features**:
  - RESTful API endpoints for comments and replies
  - Request validation using Pydantic models
  - User authentication via `UserDependency`
  - Standardized response formatting
  - Support for query parameters (status, my_comments filtering)
  - POST method usage for endpoints with potentially large obj_ids arrays to avoid URL length limitations

### 2. Service Layer (`service.py`)
- **Responsibility**: Business logic and orchestration
- **Key Features**:
  - Comment and reply CRUD operations
  - Comment status management (open/resolve)
  - Permission validation and ownership verification
  - Error handling and logging
  - Data transformation and validation

### 3. Data Layer (`data.py`)
- **Responsibility**: Database operations and data persistence
- **Key Features**:
  - Optimized query execution for comments and replies
  - Parameter sanitization and validation
  - Error handling and logging
  - Connection management
  - Support for tagged users and status updates

### 4. Query Layer (`queries.py`)
- **Responsibility**: SQL query definitions
- **Key Features**:
  - Parameterized queries for comments and replies
  - Efficient indexing strategy
  - Join operations for user information
  - Aggregation queries for highlights
  - Support for status and user filtering

## Data Models

### CommentCreate
```python
class CommentCreate(BaseModel):
    obj_id: str                    # Object ID
    obj_type: str                  # Object type
    cell_id: str                   # Cell ID
    text: str                      # Comment text
    tagged_users: Optional[List[int]]  # List of tagged user IDs
```

### CommentUpdate
```python
class CommentUpdate(BaseModel):
    text: str                      # Updated comment text
    tagged_users: Optional[List[int]]  # Updated list of tagged user IDs
```

### CommentStatusUpdate
```python
class CommentStatusUpdate(BaseModel):
    status: CommentStatusEnum      # New comment status (open/resolve)
```

### CommentReplyCreate
```python
class CommentReplyCreate(BaseModel):
    comment_id: str                # Parent comment ID
    text: str                      # Reply text
    tagged_users: Optional[List[int]]  # List of tagged user IDs
```

### CommentReplyUpdate
```python
class CommentReplyUpdate(BaseModel):
    text: str                      # Updated reply text
    tagged_users: Optional[List[int]]  # Updated list of tagged user IDs
```

### CommentFilter
```python
class CommentFilter(BaseModel):
    obj_type: str                  # Object type
    obj_ids: Optional[List[str]]   # Optional list of object IDs
```

### CommentListRequest
```python
class CommentListRequest(BaseModel):
    obj_ids: Optional[List[str]]   # Optional list of object IDs to filter comments (can be large arrays)
    status: Optional[str]          # Filter by comment status (open/resolve)
    my_comments: bool             # Show only user's comments and replies
```

### CommentResponse
```python
class CommentResponse(BaseModel):
    comment_id: str
    obj_id: str
    obj_type: str
    cell_id: str
    text: str
    tagged_users: Optional[List[int]]
    status: str
    created_by: int
    created_at: str
    updated_by: Optional[int]
    updated_at: Optional[str]
    created_by_user: str
    updated_by_user: Optional[str]
    replies: List[Dict]               # List of reply objects
```

### CommentReplyResponse
```python
class CommentReplyResponse(BaseModel):
    reply_id: str
    comment_id: str
    text: str
    tagged_users: Optional[List[int]]
    created_by: int
    created_at: str
    updated_by: Optional[int]
    updated_at: Optional[str]
    created_by_user: str
    updated_by_user: Optional[str]
```

### CommentHighlightResponse
```python
class CommentHighlightResponse(BaseModel):
    obj_id: str
    obj_type: str
    cell_id: str
    comment_count: int
```

## Security & Permissions

### Access Control
- **Create**: Any authenticated user can create comments and replies
- **Read**: Any authenticated user can read comments and replies (with filtering options for my_comments)
- **Update**: Only the comment/reply creator can update their content (enforced via ownership verification)
- **Delete**: Only the comment/reply creator can delete their content (enforced via ownership verification)
- **Status Update**: Any authenticated user can update comment status (open/resolve)

### Data Validation
- Input sanitization for all text fields
- Required field validation with custom error messages
- User authentication via Firebase
- Ownership verification for modifications
- Field length validation (255 character limit for IDs)

## Performance Considerations

### Database Optimization
- **Indexing Strategy**: Optimized indexes for common query patterns (obj_id, obj_type, cell_id, comment_id for replies)
- **Query Optimization**: Efficient JOIN operations with user_master table for user names
- **Connection Management**: Async database operations using asyncpg
- **Cascade Deletes**: Automatic cleanup of replies when comments are deleted
- **Result Processing**: Efficient grouping of comments with their replies in data layer

### Caching Strategy
- No caching implemented currently (simple read/write operations)
- Can be extended with Redis for high-traffic scenarios
- Database result processing includes optimization for large result sets

## Error Handling

### Standardized Error Responses
- **400 Bad Request**: Invalid input data or validation errors
- **401 Unauthorized**: Authentication required
- **403 Forbidden**: Insufficient permissions
- **404 Not Found**: Comment or reply doesn't exist
- **500 Internal Server Error**: Server-side errors

### Error Messages
- User-friendly error descriptions
- Detailed logging for debugging
- Consistent error response format
- Custom validation error messages

## Monitoring & Observability

### Key Metrics
- Comment and reply creation rate
- Comment status update rate
- API response times by endpoint
- Error rates by endpoint
- Database query performance
- User engagement metrics

### Logging
- Structured logging for all operations
- User action tracking with context
- Performance metrics logging
- Error logging with detailed context
- Audit trail for comment modifications

## Future Enhancements

### Potential Features
- Deeper comment threading with nested replies (currently supports one-level replies)
- Comment notifications and email alerts for tagged users
- Advanced comment search and full-text filtering
- Comment analytics and reporting dashboards
- Bulk comment operations (create, update, delete)
- Comment export functionality (CSV, Excel)
- Comment templates and quick responses
- Real-time comment updates via WebSockets
- Comment mention system with @ symbol support
- Comment attachment support (files, images)

### Scalability Considerations
- Database partitioning for large datasets
- Read replicas for high-traffic scenarios
- Horizontal scaling with load balancers
- Microservice architecture migration
- Real-time comment updates via WebSockets

## Integration Points

### Dependencies
- **FastAPI**: Web framework
- **PostgreSQL**: Primary database
- **asyncpg**: Async PostgreSQL driver
- **Pydantic**: Data validation and serialization
- **Firebase Admin**: Authentication
- **Google Cloud Secret Manager**: Configuration

### External Systems
- **User Management**: Global user_master table
- **Authentication**: Firebase integration
- **Configuration**: Environment-based settings
- **Logging**: Centralized logging system
- **Notification System**: Future integration for user tagging

*This comprehensive documentation covers all aspects of the enhanced PriceSmart Comments System, including the new reply functionality, status management, and user tagging features. It provides a complete reference for development, deployment, and maintenance teams.*
