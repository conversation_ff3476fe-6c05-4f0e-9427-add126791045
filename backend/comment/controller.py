from fastapi import APIRouter
from typing import Optional
from app.dependencies import UserDependency
from comment import constants as comment_constants
from comment import models as comment_models
from comment import service as comment_service
from pricesmart_common import utils as common_utils
from exceptions.exceptions import CommonException
import logging

# Set up logging
logger = logging.getLogger(__name__)

router = APIRouter(tags=[comment_constants.COMMENT_API_TAG])


@router.post("/comment")
async def create_comment(comment_data: comment_models.CommentCreate, user_id: UserDependency):
    """Create a new comment for a cell"""
    try:
        data = await comment_service.create_comment(comment_data, user_id)
        return common_utils.create_response(
            data=data, 
            message="Comment created successfully."
        )
    except Exception as e:
        logger.error(f"Error in create_comment: {str(e)}")
        raise CommonException(f"Failed to create comment: {str(e)}")


@router.get("/comment/{comment_id}")
async def get_comment(comment_id: str):
    """Get a specific comment by ID"""
    try:
        data = await comment_service.get_comment_by_id(comment_id)
        return common_utils.create_response(data=data)
    except Exception as e:
        logger.error(f"Error in get_comment: {str(e)}")
        raise CommonException(f"Failed to retrieve comment: {str(e)}")


@router.patch("/comment/{comment_id}")
async def update_comment(comment_id: str, comment_data: comment_models.CommentUpdate, user_id: UserDependency):
    """Update a comment"""
    try:
        data = await comment_service.update_comment(comment_id, comment_data, user_id)
        return common_utils.create_response(
            data=data, 
            message="Comment updated successfully."
        )
    except Exception as e:
        logger.error(f"Error in update_comment: {str(e)}")
        raise CommonException(f"Failed to update comment: {str(e)}")


@router.delete("/comment/{comment_id}")
async def delete_comment(comment_id: str, user_id: UserDependency):
    """Delete a comment"""
    try:
        await comment_service.delete_comment(comment_id, user_id)
        return common_utils.create_response(message="Comment deleted successfully.")
    except Exception as e:
        logger.error(f"Error in delete_comment: {str(e)}")
        raise CommonException(f"Failed to delete comment: {str(e)}")


@router.patch("/comment/{comment_id}/status")
async def update_comment_status(comment_id: str, status_data: comment_models.CommentStatusUpdate, user_id: UserDependency):
    """Update comment status (open/resolve)"""
    try:
        data = await comment_service.update_comment_status(comment_id, status_data, user_id)
        return common_utils.create_response(
            data=data, 
            message=f"Comment status updated to {status_data.status.value} successfully."
        )
    except Exception as e:
        logger.error(f"Error in update_comment_status: {str(e)}")
        raise CommonException(f"Failed to update comment status: {str(e)}")


@router.get("/comment/{obj_type}/{obj_id}/{cell_id}")
async def get_comments_by_cell(obj_type: str, obj_id: str, cell_id: str, status: str = None, my_comments: bool = False, user_id: UserDependency = None):
    """Get all comments with replies for a specific cell with optional filtering"""
    try:
        data = await comment_service.get_comments_by_cell(obj_type, obj_id, cell_id, status, my_comments, user_id)
        return common_utils.create_response(data=data)
    except Exception as e:
        logger.error(f"Error in get_comments_by_cell: {str(e)}")
        raise CommonException(f"Failed to retrieve comments: {str(e)}")


@router.post("/comments/{obj_type}")
async def get_comments_by_obj_type(
    obj_type: str,
    request_data: comment_models.CommentListRequest,
    user_id: UserDependency = None
):
    """Get comments with replies for a specific object type with optional filtering
    
    Uses POST method to handle potentially large arrays of obj_ids in request body.
    This avoids URL length limitations that can occur with GET query parameters.
    
    Filtered comments example:
    {
        "obj_ids": ["promo_123", "promo_456", "promo_789"],
        "status": "open",
        "my_comments": false
    }
    
    All comments example (no filtering):
    {
        "obj_ids": null,
        "status": null,
        "my_comments": false
    }
    """
    try:
        data = await comment_service.get_comments_by_obj_type(
            obj_type, 
            request_data.obj_ids, 
            request_data.status, 
            request_data.my_comments, 
            user_id
        )
        return common_utils.create_response(data=data)
    except Exception as e:
        logger.error(f"Error in get_comments_by_obj_type: {str(e)}")
        raise CommonException(f"Failed to retrieve comments: {str(e)}")


@router.post("/comments/highlights")
async def get_comment_highlights(filter_data: comment_models.CommentFilter):
    """Get comment highlights for cells with comments
    
    Use POST method to handle potentially large arrays of obj_ids in request body.
    This avoids URL length limitations that can occur with GET query parameters.
    
    Example request body:
    {
        "obj_type": "promotion",
        "obj_ids": ["promo_001", "promo_002", "promo_003"]
    }
    
    For highlights of all objects, send:
    {
        "obj_type": "promotion",
        "obj_ids": null
    }
    """
    try:
        data = await comment_service.get_comment_highlights(filter_data)
        return common_utils.create_response(data=data)
    except Exception as e:
        logger.error(f"Error in get_comment_highlights: {str(e)}")
        raise CommonException(f"Failed to retrieve comment highlights: {str(e)}")


# =====================================================
# COMMENT REPLY ENDPOINTS
# =====================================================

@router.post("/comment/reply")
async def create_comment_reply(reply_data: comment_models.CommentReplyCreate, user_id: UserDependency):
    """Create a new reply to a comment"""
    try:
        data = await comment_service.create_comment_reply(reply_data, user_id)
        return common_utils.create_response(
            data=data, 
            message="Reply created successfully."
        )
    except Exception as e:
        logger.error(f"Error in create_comment_reply: {str(e)}")
        raise CommonException(f"Failed to create reply: {str(e)}")


@router.get("/comment/reply/{reply_id}")
async def get_comment_reply(reply_id: str):
    """Get a specific reply by ID"""
    try:
        data = await comment_service.get_comment_reply_by_id(reply_id)
        return common_utils.create_response(data=data)
    except Exception as e:
        logger.error(f"Error in get_comment_reply: {str(e)}")
        raise CommonException(f"Failed to retrieve reply: {str(e)}")


@router.patch("/comment/reply/{reply_id}")
async def update_comment_reply(reply_id: str, reply_data: comment_models.CommentReplyUpdate, user_id: UserDependency):
    """Update a reply"""
    try:
        data = await comment_service.update_comment_reply(reply_id, reply_data, user_id)
        return common_utils.create_response(
            data=data, 
            message="Reply updated successfully."
        )
    except Exception as e:
        logger.error(f"Error in update_comment_reply: {str(e)}")
        raise CommonException(f"Failed to update reply: {str(e)}")


@router.delete("/comment/reply/{reply_id}")
async def delete_comment_reply(reply_id: str, user_id: UserDependency):
    """Delete a reply"""
    try:
        await comment_service.delete_comment_reply(reply_id, user_id)
        return common_utils.create_response(message="Reply deleted successfully.")
    except Exception as e:
        logger.error(f"Error in delete_comment_reply: {str(e)}")
        raise CommonException(f"Failed to delete reply: {str(e)}")



