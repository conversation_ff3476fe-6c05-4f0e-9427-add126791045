from typing import Dict, List, Optional, Set, Union
from fastapi import WebSocket
from dataclasses import dataclass
import json
from logger.logger import logger
from datetime import datetime
from abc import ABC, abstractmethod
import uuid


@dataclass
class WebSocketConnection:
    """Represents a websocket connection with user information"""
    user_id: int
    websocket: WebSocket
    connection_id: str = None
    connected_at: str = None
    
    def __post_init__(self):
        if self.connection_id is None:
            self.connection_id = str(uuid.uuid4())
        if self.connected_at is None:
            self.connected_at = datetime.utcnow().isoformat()


class BaseWebSocketManager(ABC):
    """
    Base class for WebSocket managers with common functionality.
    
    Provides shared methods for:
    - Connection management
    - Message serialization
    - WebSocket cleanup
    - Connection tracking
    """

    def __init__(self):
        self.active_connections: Set[WebSocket] = set()

    def _serialize_message_data(self, message_data: Union[dict, list]) -> Union[dict, list]:
        """
        Serialize message data to JSON-safe format.
        Converts datetime objects to ISO strings and handles other non-serializable types.
        Supports both dictionaries and arrays.
        """
        if not message_data:
            return message_data
        
        # Handle arrays/lists
        if isinstance(message_data, (list, tuple)):
            serialized_list = []
            for item in message_data:
                if isinstance(item, (dict, list)):
                    serialized_list.append(self._serialize_message_data(item))
                elif hasattr(item, '__class__') and item.__class__.__name__ == 'UUID':
                    # Handle UUID objects in lists
                    serialized_list.append(str(item))
                else:
                    serialized_list.append(item)
            return serialized_list
        
        # Handle dictionaries
        if isinstance(message_data, dict):
            serialized = {}
            for key, value in message_data.items():
                if isinstance(value, datetime):
                    serialized[key] = value.isoformat()
                elif hasattr(value, '__class__') and value.__class__.__name__ == 'UUID':
                    # Handle UUID objects
                    serialized[key] = str(value)
                elif isinstance(value, (list, tuple)) and key == 'tagged_users':
                    # Ensure tagged_users is a proper list
                    serialized[key] = list(value) if value else []
                elif isinstance(value, (int, str, bool, float, type(None))):
                    serialized[key] = value
                elif isinstance(value, dict):
                    serialized[key] = self._serialize_message_data(value)
                elif isinstance(value, (list, tuple)):
                    serialized[key] = [self._serialize_message_data(item) if isinstance(item, (dict, list)) else item for item in value]
                else:
                    # For any other types, convert to string
                    serialized[key] = str(value)
            
            return serialized
        
        # For any other types, return as is
        return message_data

    def _get_timestamp(self) -> str:
        """Get current timestamp as ISO string."""
        return datetime.utcnow().isoformat()

    async def _cleanup_websocket(self, websocket: WebSocket):
        """Clean up a websocket connection."""
        try:
            self.active_connections.discard(websocket)
            # Check if websocket is still connected before trying to close
            try:
                if hasattr(websocket, "client_state") and hasattr(websocket.client_state, "disconnected") and not websocket.client_state.disconnected:
                    await websocket.close()
                elif not hasattr(websocket, "client_state"):
                    # Fallback: try to close anyway
                    await websocket.close()
            except Exception as close_error:
                # Websocket might already be closed, log but don't fail
                logger.debug(f"Websocket already closed or error closing: {str(close_error)}")
        except Exception as e:
            logger.debug(f"Error cleaning up websocket: {str(e)}")

    def _is_websocket_disconnected(self, websocket: WebSocket) -> bool:
        """Check if websocket is disconnected."""
        return (
            hasattr(websocket, "client_state")
            and hasattr(websocket.client_state, "disconnected")
            and websocket.client_state.disconnected
        )

    def _json_serializer(self, obj):
        """
        Custom JSON serializer to handle non-serializable objects.
        """
        if hasattr(obj, '__class__') and obj.__class__.__name__ == 'UUID':
            return str(obj)
        elif isinstance(obj, datetime):
            return obj.isoformat()
        else:
            return str(obj)

    async def _send_message_safely(self, websocket: WebSocket, message: dict, user_id: int, context: str) -> bool:
        """
        Safely send a message to a websocket with error handling.
        
        Args:
            websocket: The WebSocket to send to
            message: The message to send
            user_id: User ID for logging
            context: Context string for logging
            
        Returns:
            bool: True if message sent successfully, False otherwise
        """
        try:
            json_message = json.dumps(message, default=self._json_serializer)
            await websocket.send_text(json_message)
            logger.debug(f"Sent {context} to user {user_id}")
            return True
        except TypeError as json_error:
            logger.error(f"JSON serialization error for user {user_id}: {str(json_error)}")
            logger.error(f"Message data that failed to serialize: {message}")
            return False
        except Exception as e:
            logger.error(f"Error sending {context} to user {user_id}: {str(e)}")
            return False



    @abstractmethod
    async def subscribe(self, identifier: Union[str, int], user_id: int, websocket: WebSocket) -> bool:
        """Subscribe a user to websocket notifications."""
        pass

    @abstractmethod
    async def unsubscribe(self, identifier: Union[str, int], user_id: int) -> bool:
        """Unsubscribe a user from websocket notifications."""
        pass

    @abstractmethod
    async def disconnect(self, websocket: WebSocket):
        """Handle websocket disconnection."""
        pass

    @abstractmethod
    def get_connected_users(self, identifier: Union[str, int]) -> List[int]:
        """Get list of connected user IDs."""
        pass

    @abstractmethod
    def get_connection_stats(self) -> Dict[str, int]:
        """Get connection statistics."""
        pass


class WebSocketManager(BaseWebSocketManager):
    """
    Manages WebSocket connections for chat topic updates.

    Stores connections by object type and provides methods to:
    - Subscribe/unsubscribe users to object types
    - Broadcast notifications to specific users
    - Handle connection cleanup
    """

    def __init__(self):
        super().__init__()
        # Structure: { obj_type: [WebSocketConnection, ...] }
        self.connections: Dict[str, List[WebSocketConnection]] = {}

    async def subscribe(self, obj_type: str, user_id: int, websocket: WebSocket) -> bool:
        """
        Subscribe a user to websocket notifications for an object type.
        Allows multiple connections per user (multiple tabs).

        Args:
            obj_type: The object type (promo, event, store_group, product)
            user_id: The user ID to subscribe
            websocket: The WebSocket connection

        Returns:
            bool: True if subscription successful, False otherwise
        """
        try:
            await websocket.accept()
            self.active_connections.add(websocket)

            # Initialize object type list if doesn't exist
            if obj_type not in self.connections:
                self.connections[obj_type] = []

            # Add new connection (allow multiple connections per user)
            connection = WebSocketConnection(user_id=user_id, websocket=websocket)
            self.connections[obj_type].append(connection)

            logger.info(f"User {user_id} subscribed to {obj_type} websocket with connection_id: {connection.connection_id}")
            logger.debug(f"Total connections for {obj_type}: {len(self.connections[obj_type])}")
            return True

        except Exception as e:
            logger.error(f"Error subscribing user {user_id} to {obj_type}: {str(e)}")
            await self._cleanup_websocket(websocket)
            return False

    async def unsubscribe(self, obj_type: str, user_id: int) -> bool:
        """
        Unsubscribe a user from websocket notifications for an object type.

        Args:
            obj_type: The object type
            user_id: The user ID to unsubscribe

        Returns:
            bool: True if unsubscription successful, False otherwise
        """
        try:
            if obj_type not in self.connections:
                return False

            connection = self._find_user_connection(obj_type, user_id)
            if connection:
                await self._remove_connection(obj_type, connection)
                logger.info(f"User {user_id} unsubscribed from {obj_type} websocket")
                return True

            return False

        except Exception as e:
            logger.error(f"Error unsubscribing user {user_id} from {obj_type}: {str(e)}")
            return False

    async def disconnect(self, websocket: WebSocket):
        """
        Handle websocket disconnection by removing from all subscriptions.

        Args:
            websocket: The WebSocket connection that was disconnected
        """
        try:
            # Find and remove the connection from all object types
            for obj_type in list(self.connections.keys()):
                connections = self.connections[obj_type]
                for connection in connections[:]:  # Create a copy to iterate safely
                    if connection.websocket == websocket:
                        await self._remove_connection(obj_type, connection)
                        logger.info(f"Removed disconnected websocket for user {connection.user_id} from {obj_type}")
                        break

            await self._cleanup_websocket(websocket)

        except Exception as e:
            logger.error(f"Error handling websocket disconnect: {str(e)}")

    async def broadcast_to_users(self, obj_type: str, user_data_map: Dict[int, dict], 
                                action: str, topic_id: str, object_ids: List[str], 
                                triggered_user: int):
        """
        Broadcast different data to specific users for an object type with enhanced message structure.

        Args:
            obj_type: The object type to broadcast to
            user_data_map: Dictionary mapping user_id to their specific data
            action: The action that triggered this broadcast (from constants)
            topic_id: The topic ID involved in the action
            object_ids: List of object IDs for the topic
            triggered_user: The user who triggered the action
        """
        if obj_type not in self.connections:
            logger.debug(f"No connections found for object type: {obj_type}")
            return

        connections = self.connections[obj_type][:]  # Create a copy to iterate safely

        for connection in connections:
            user_id = connection.user_id

            # Skip if no data for this user
            if user_id not in user_data_map:
                continue

            try:
                data = user_data_map[user_id]
                # Serialize data to ensure JSON compatibility
                serialized_data = self._serialize_message_data(data)
                
                message = {
                    "type": "topic_update",
                    "obj_type": obj_type,
                    "action": action,
                    "topic_id": topic_id,
                    "object_ids": object_ids,
                    "triggered_user": triggered_user,
                    "data": serialized_data,
                    "timestamp": self._get_timestamp(),
                }

                success = await self._send_message_safely(
                    connection.websocket, message, user_id, f"topic update for {obj_type} with action {action}"
                )
                if not success:
                    await self._remove_connection(obj_type, connection)

            except Exception as e:
                logger.error(f"Error processing message for user {user_id}: {str(e)}")
                await self._remove_connection(obj_type, connection)

    def get_connected_users(self, obj_type: str) -> List[int]:
        """
        Get list of unique user IDs connected to an object type.

        Args:
            obj_type: The object type

        Returns:
            List[int]: List of unique connected user IDs
        """
        if obj_type not in self.connections:
            return []

        # Return unique user IDs (in case of multiple connections per user)
        return list(set(conn.user_id for conn in self.connections[obj_type]))

    def get_connection_stats(self) -> Dict[str, int]:
        """
        Get statistics about current connections.

        Returns:
            Dict containing connection counts by object type
        """
        stats = {}
        for obj_type, connections in self.connections.items():
            stats[obj_type] = len(connections)
        stats["total_active"] = len(self.active_connections)
        return stats

    def get_detailed_connection_stats(self, obj_type: str = None) -> Dict[str, any]:
        """
        Get detailed statistics about current connections including connection info.

        Args:
            obj_type: Optional specific object type to get stats for

        Returns:
            Dict containing detailed connection information
        """
        if obj_type:
            if obj_type not in self.connections:
                return {
                    "summary": {"total_connections": 0, "unique_users": 0},
                    "connections": [],
                    "timestamp": datetime.utcnow().isoformat()
                }
            
            connections = self.connections[obj_type]
            unique_users = len(set(conn.user_id for conn in connections))
            
            connection_info = []
            for conn in connections:
                connection_info.append({
                    "user_id": conn.user_id,
                    "connection_id": conn.connection_id,
                    "connected_at": conn.connected_at
                })
            
            return {
                "summary": {
                    "total_connections": len(connections),
                    "unique_users": unique_users
                },
                "connections": connection_info,
                "timestamp": datetime.utcnow().isoformat()
            }
        else:
            # Get stats for all object types
            all_stats = {}
            total_connections = 0
            total_unique_users = set()
            
            for obj_type, connections in self.connections.items():
                unique_users = set(conn.user_id for conn in connections)
                total_unique_users.update(unique_users)
                total_connections += len(connections)
                
                connection_info = []
                for conn in connections:
                    connection_info.append({
                        "user_id": conn.user_id,
                        "connection_id": conn.connection_id,
                        "connected_at": conn.connected_at
                    })
                
                all_stats[obj_type] = {
                    "summary": {
                        "total_connections": len(connections),
                        "unique_users": len(unique_users)
                    },
                    "connections": connection_info,
                    "timestamp": datetime.utcnow().isoformat()
                }
            
            return {
                "object_types": all_stats,
                "summary": {
                    "total_connections": total_connections,
                    "total_unique_users": len(total_unique_users),
                    "total_active_websockets": len(self.active_connections)
                },
                "timestamp": datetime.utcnow().isoformat()
            }

    def _find_user_connections(self, obj_type: str, user_id: int) -> List[WebSocketConnection]:
        """Find all connections for a user for a specific object type."""
        if obj_type not in self.connections:
            return []

        return [conn for conn in self.connections[obj_type] if conn.user_id == user_id]

    def _find_user_connection(self, obj_type: str, user_id: int) -> Optional[WebSocketConnection]:
        """Find a user's first connection for a specific object type (for backward compatibility)."""
        connections = self._find_user_connections(obj_type, user_id)
        return connections[0] if connections else None

    async def _remove_connection(self, obj_type: str, connection: WebSocketConnection):
        """Remove a connection from the manager."""
        try:
            if obj_type in self.connections:
                self.connections[obj_type].remove(connection)

                # Clean up empty object type lists
                if not self.connections[obj_type]:
                    del self.connections[obj_type]

            await self._cleanup_websocket(connection.websocket)

        except Exception as e:
            logger.error(f"Error removing connection: {str(e)}")




class TopicWebSocketManager(BaseWebSocketManager):
    """
    WebSocket manager for individual topic message updates.
    Manages connections per topic and broadcasts messages to topic members.
    """

    def __init__(self):
        super().__init__()
        # Structure: { topic_id: [WebSocketConnection, ...] }
        self.topic_connections: Dict[str, List[WebSocketConnection]] = {}

    async def subscribe(self, topic_id: str, user_id: int, websocket: WebSocket) -> bool:
        """
        Subscribe a user to websocket notifications for a specific topic.
        Allows multiple connections per user (multiple tabs).

        Args:
            topic_id: The topic ID to subscribe to
            user_id: The user ID to subscribe
            websocket: The WebSocket connection

        Returns:
            bool: True if subscription successful, False otherwise
        """
        try:
            await websocket.accept()
            self.active_connections.add(websocket)

            # Initialize topic list if doesn't exist
            if topic_id not in self.topic_connections:
                self.topic_connections[topic_id] = []

            # Add new connection (allow multiple connections per user)
            connection = WebSocketConnection(user_id=user_id, websocket=websocket)
            self.topic_connections[topic_id].append(connection)

            logger.info(f"User {user_id} subscribed to topic {topic_id} websocket with connection_id: {connection.connection_id}")
            logger.debug(f"Total connections for topic {topic_id}: {len(self.topic_connections[topic_id])}")
            return True

        except Exception as e:
            logger.error(f"Error subscribing user {user_id} to topic {topic_id}: {str(e)}")
            await self._cleanup_websocket(websocket)
            return False

    async def unsubscribe(self, topic_id: str, user_id: int) -> bool:
        """
        Unsubscribe a user from websocket notifications for a specific topic.

        Args:
            topic_id: The topic ID
            user_id: The user ID to unsubscribe

        Returns:
            bool: True if unsubscription successful, False otherwise
        """
        try:
            if topic_id not in self.topic_connections:
                return False

            connection = self._find_user_topic_connection(topic_id, user_id)
            if connection:
                await self._remove_topic_connection(topic_id, connection)
                logger.info(f"User {user_id} unsubscribed from topic {topic_id} websocket")
                return True

            return False

        except Exception as e:
            logger.error(f"Error unsubscribing user {user_id} from topic {topic_id}: {str(e)}")
            return False

    async def disconnect(self, websocket: WebSocket):
        """
        Handle websocket disconnection by removing from all topic subscriptions.

        Args:
            websocket: The WebSocket connection that was disconnected
        """
        try:
            # Find and remove the connection from all topics
            for topic_id in list(self.topic_connections.keys()):
                connections = self.topic_connections[topic_id]
                for connection in connections[:]:  # Create a copy to iterate safely
                    if connection.websocket == websocket:
                        await self._remove_topic_connection(topic_id, connection)
                        logger.info(f"Removed disconnected websocket for user {connection.user_id} from topic {topic_id}")
                        break

            await self._cleanup_websocket(websocket)

        except Exception as e:
            logger.error(f"Error handling topic websocket disconnect: {str(e)}")

    async def broadcast_message_to_topic(self, topic_id: str, message_data: dict, sender_id: int, 
                                       topic_members: List[int]) -> List[int]:
        """
        Broadcast a message to all connected members of a topic.
        Returns list of user IDs who were not connected (for unread count updates).
        Only returns offline users for new messages, not for updates/deletions.

        Args:
            topic_id: The topic ID to broadcast to
            message_data: The message data to broadcast
            sender_id: The user ID who sent the message
            topic_members: List of all member user IDs in the topic

        Returns:
            List[int]: List of user IDs who were not connected (need unread count update)
        """
        if topic_id not in self.topic_connections:
            logger.debug(f"No connections found for topic: {topic_id}")
            return topic_members

        connected_users = set()
        connections = self.topic_connections[topic_id][:]  # Create a copy to iterate safely

        # Serialize message data to ensure JSON compatibility
        serialized_message_data = self._serialize_message_data(message_data)
        
        message = {
            "type": "new_message",
            "topic_id": topic_id,
            "message": serialized_message_data,
            "sender_id": sender_id,
            "timestamp": self._get_timestamp(),
        }

        for connection in connections:
            user_id = connection.user_id

            # Skip sender
            if user_id == sender_id:
                connected_users.add(user_id)
                continue

            try:
                success = await self._send_message_safely(
                    connection.websocket, message, user_id, f"message for topic {topic_id}"
                )
                if success:
                    connected_users.add(user_id)
                else:
                    await self._remove_topic_connection(topic_id, connection)

            except Exception as e:
                logger.error(f"Error processing message for user {user_id}: {str(e)}")
                await self._remove_topic_connection(topic_id, connection)

        # Return list of users who were not connected (need unread count update)
        offline_users = [user_id for user_id in topic_members if user_id not in connected_users and user_id != sender_id]
        return offline_users

    async def broadcast_message_update_to_topic(self, topic_id: str, message_data: dict, sender_id: int, 
                                              topic_members: List[int], action: str) -> None:
        """
        Broadcast a message update or deletion to all connected members of a topic.
        No unread count updates needed for these actions.

        Args:
            topic_id: The topic ID to broadcast to
            message_data: The message data to broadcast
            sender_id: The user ID who performed the action
            topic_members: List of all member user IDs in the topic
            action: The action type (MESSAGE_UPDATED, MESSAGE_DELETED)
        """
        if topic_id not in self.topic_connections:
            logger.debug(f"No connections found for topic: {topic_id}")
            return

        connections = self.topic_connections[topic_id][:]  # Create a copy to iterate safely

        # Serialize message data to ensure JSON compatibility
        serialized_message_data = self._serialize_message_data(message_data)
        
        message = {
            "type": "message_update",
            "topic_id": topic_id,
            "message": serialized_message_data,
            "sender_id": sender_id,
            "action": action,
            "timestamp": self._get_timestamp(),
        }

        for connection in connections:
            user_id = connection.user_id

            # Skip sender
            if user_id == sender_id:
                continue

            try:
                success = await self._send_message_safely(
                    connection.websocket, message, user_id, f"message {action} for topic {topic_id}"
                )
                if not success:
                    await self._remove_topic_connection(topic_id, connection)

            except Exception as e:
                logger.error(f"Error processing message {action} for user {user_id}: {str(e)}")
                await self._remove_topic_connection(topic_id, connection)

    def get_connected_users(self, topic_id: str) -> List[int]:
        """
        Get list of unique user IDs connected to a specific topic.

        Args:
            topic_id: The topic ID

        Returns:
            List[int]: List of unique connected user IDs
        """
        if topic_id not in self.topic_connections:
            return []

        # Return unique user IDs (in case of multiple connections per user)
        return list(set(conn.user_id for conn in self.topic_connections[topic_id]))

    def get_connection_stats(self) -> Dict[str, int]:
        """
        Get statistics about current topic connections.

        Returns:
            Dict containing connection counts by topic ID
        """
        stats = {}
        for topic_id, connections in self.topic_connections.items():
            stats[f"topic_{topic_id}"] = len(connections)
        stats["total_active"] = len(self.active_connections)
        return stats

    def get_detailed_connection_stats(self, topic_id: str = None) -> Dict[str, any]:
        """
        Get detailed statistics about current topic connections including connection info.

        Args:
            topic_id: Optional specific topic ID to get stats for

        Returns:
            Dict containing detailed connection information
        """
        if topic_id:
            if topic_id not in self.topic_connections:
                return {
                    "summary": {"total_connections": 0, "unique_users": 0},
                    "connections": [],
                    "timestamp": datetime.utcnow().isoformat()
                }
            
            connections = self.topic_connections[topic_id]
            unique_users = len(set(conn.user_id for conn in connections))
            
            connection_info = []
            for conn in connections:
                connection_info.append({
                    "user_id": conn.user_id,
                    "connection_id": conn.connection_id,
                    "connected_at": conn.connected_at
                })
            
            return {
                "summary": {
                    "total_connections": len(connections),
                    "unique_users": unique_users
                },
                "connections": connection_info,
                "timestamp": datetime.utcnow().isoformat()
            }
        else:
            # Get stats for all topics
            all_stats = {}
            total_connections = 0
            total_unique_users = set()
            
            for topic_id, connections in self.topic_connections.items():
                unique_users = set(conn.user_id for conn in connections)
                total_unique_users.update(unique_users)
                total_connections += len(connections)
                
                connection_info = []
                for conn in connections:
                    connection_info.append({
                        "user_id": conn.user_id,
                        "connection_id": conn.connection_id,
                        "connected_at": conn.connected_at
                    })
                
                all_stats[topic_id] = {
                    "summary": {
                        "total_connections": len(connections),
                        "unique_users": len(unique_users)
                    },
                    "connections": connection_info,
                    "timestamp": datetime.utcnow().isoformat()
                }
            
            return {
                "topics": all_stats,
                "summary": {
                    "total_connections": total_connections,
                    "total_unique_users": len(total_unique_users),
                    "total_active_websockets": len(self.active_connections)
                },
                "timestamp": datetime.utcnow().isoformat()
            }

    def _find_user_topic_connections(self, topic_id: str, user_id: int) -> List[WebSocketConnection]:
        """Find all connections for a user for a specific topic."""
        if topic_id not in self.topic_connections:
            return []

        return [conn for conn in self.topic_connections[topic_id] if conn.user_id == user_id]

    def _find_user_topic_connection(self, topic_id: str, user_id: int) -> Optional[WebSocketConnection]:
        """Find a user's first connection for a specific topic (for backward compatibility)."""
        connections = self._find_user_topic_connections(topic_id, user_id)
        return connections[0] if connections else None

    async def _remove_topic_connection(self, topic_id: str, connection: WebSocketConnection):
        """Remove a connection from the topic manager."""
        try:
            if topic_id in self.topic_connections:
                self.topic_connections[topic_id].remove(connection)

                # Clean up empty topic lists
                if not self.topic_connections[topic_id]:
                    del self.topic_connections[topic_id]

            await self._cleanup_websocket(connection.websocket)

        except Exception as e:
            logger.error(f"Error removing topic connection: {str(e)}")



    # Alias methods for backward compatibility
    async def subscribe_to_topic(self, topic_id: str, user_id: int, websocket: WebSocket) -> bool:
        """Alias for subscribe method for backward compatibility."""
        return await self.subscribe(topic_id, user_id, websocket)

    async def unsubscribe_from_topic(self, topic_id: str, user_id: int) -> bool:
        """Alias for unsubscribe method for backward compatibility."""
        return await self.unsubscribe(topic_id, user_id)

    def get_connected_users_for_topic(self, topic_id: str) -> List[int]:
        """Alias for get_connected_users method for backward compatibility."""
        return self.get_connected_users(topic_id)

    def get_topic_connection_stats(self) -> Dict[str, int]:
        """Alias for get_connection_stats method for backward compatibility."""
        return self.get_connection_stats()


# Global websocket manager instances
websocket_manager = WebSocketManager()
topic_websocket_manager = TopicWebSocketManager()
