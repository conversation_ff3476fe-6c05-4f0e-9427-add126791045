from typing import Dict, List
from pydantic import BaseModel
from enum import Enum


class WebSocketEventType(str, Enum):
    """Types of websocket events"""
    TOPIC_UPDATE = "topic_update"
    MEMBER_UPDATE = "member_update"
    CONNECTION_STATUS = "connection_status"
    ERROR = "error"


class WebSocketAction(str, Enum):
    """Types of websocket actions"""
    TOPIC_CREATED = "topic_created"
    TOPIC_UPDATED = "topic_updated"
    TOPIC_DELETED = "topic_deleted"
    MEMBERS_ADDED = "members_added"
    MEMBERS_REMOVED = "members_removed"
    MESSAGE_CREATED = "message_created"
    MESSAGE_UPDATED = "message_updated"
    MESSAGE_DELETED = "message_deleted"



class WebSocketConnectionInfo(BaseModel):
    """Detailed information about a WebSocket connection"""
    user_id: int
    connection_id: str
    connected_at: str


class WebSocketDetailedStats(BaseModel):
    """Detailed WebSocket statistics with connection information"""
    summary: Dict[str, int]
    connections: List[WebSocketConnectionInfo]
    timestamp: str


class TopicWebSocketDetailedStats(BaseModel):
    """Detailed Topic WebSocket statistics with connection information"""
    summary: Dict[str, int]
    connections: List[WebSocketConnectionInfo]
    timestamp: str


class AllWebSocketStats(BaseModel):
    """Complete WebSocket statistics for both object types and topics"""
    object_types: Dict[str, WebSocketDetailedStats]
    topics: Dict[str, TopicWebSocketDetailedStats]
    summary: Dict[str, int]
    timestamp: str
