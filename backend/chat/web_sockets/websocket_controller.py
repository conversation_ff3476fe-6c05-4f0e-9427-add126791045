from fastapi import APIRouter, WebSocket, WebSocketDisconnect, HTTPException, Path, Request
from typing import Dict, Any, Optional
from chat.web_sockets.websocket_manager import websocket_manager, topic_websocket_manager
from chat.web_sockets.websocket_models import (
    WebSocketDetailedStats, 
    TopicWebSocketDetailedStats, 
    AllWebSocketStats
)
from chat import constants as chat_constants
from common.utils import authenticate_token, get_user_id_from_email
from configuration.environment import environment
from logger.logger import logger
from tenant.base import get_tenant_id_context
from datetime import datetime
import json



router = APIRouter(tags=[chat_constants.CHAT_API_TAG])


async def mtp_auth_util(request: Request, token: str):
    if environment.is_local:
        import os
        return {"tenant": None,"email_id": None, "user_id": os.getenv("user_id")}
    else:
        tenant = await get_tenant_id_context(request=request)
        tenant = tenant["tenant"]
    logger.info(f"{environment.LOG_TITLE} : Tenant for this application = {tenant}")
    resp = await authenticate_token(token)
    email_id = resp["info"]["email"]
    user_id = (
        request.headers.get("user-id", 0)
        if email_id.endswith("iam.gserviceaccount.com")
        else await get_user_id_from_email(tenant, email_id)
    )

    return {"tenant": tenant, "email_id": email_id, "user_id": user_id}


async def authenticate_websocket_connection(websocket: WebSocket, endpoint_type: str, identifier: str) -> Optional[int]:
    """
    Common authentication handler for websocket connections.
    
    Args:
        websocket: The WebSocket connection (should already be accepted)
        endpoint_type: Type of endpoint for logging ("obj_type" or "topic")
        identifier: The obj_type or topic_id for logging
        
    Returns:
        int: user_id if authentication successful, None if failed
    """
    # Extract token from Authorization header
    auth_header = websocket.headers.get("Authorization")
    if not auth_header:
        error_message = {
            "type": "error",
            "message": "Authorization header required",
            "timestamp": datetime.utcnow().isoformat()
        }
        await websocket.send_text(json.dumps(error_message))
        await websocket.close()
        return None
    

    
    try:
        # Authenticate and get user_id
        auth_info = await mtp_auth_util(websocket, auth_header)
        user_id = int(auth_info["user_id"])
        logger.info(f"WebSocket connection attempt: {endpoint_type}={identifier}, user_id={user_id}")
        return user_id
    except Exception as e:
        logger.error(f"Authentication failed: {str(e)}")
        error_message = {
            "type": "error",
            "message": "Authentication failed",
            "timestamp": datetime.utcnow().isoformat()
        }
        await websocket.send_text(json.dumps(error_message))
        await websocket.close()
        return None


@router.websocket("/ws/{obj_type}")
async def websocket_endpoint(websocket: WebSocket, obj_type: str):
    """
    WebSocket endpoint for topic updates.
    
    URL pattern: /ws/{obj_type}
    - obj_type: Object type (any text)
    - Authorization: Bearer token in header (required)
    """
    

    
    # Authenticate the connection
    user_id = await authenticate_websocket_connection(websocket, "obj_type", obj_type)
    if user_id is None:
        return
    
    # Subscribe user to websocket notifications using unified interface
    success = await websocket_manager.subscribe(obj_type, user_id, websocket)
    
    if not success:
        logger.error(f"Failed to subscribe user {user_id} to {obj_type}")
        return
    
    try:
        # Send initial connection confirmation
        welcome_message = {
            "type": "connection_status",
            "obj_type": obj_type,
            "message": f"Successfully connected to {obj_type} updates",
            "timestamp": datetime.utcnow().isoformat(),
            "user_id": user_id
        }
        await websocket.send_text(json.dumps(welcome_message))
        
        # Keep connection alive and handle incoming messages
        while True:
            try:
                # Wait for messages from client (optional - can be used for ping/pong)
                data = await websocket.receive_text()
                
                # Parse client message
                try:
                    client_message = json.loads(data)
                    await _handle_client_message(websocket, obj_type, user_id, client_message)
                except json.JSONDecodeError:
                    error_message = {
                        "type": "error",
                        "message": "Invalid JSON format",
                        "timestamp": datetime.utcnow().isoformat()
                    }
                    await websocket.send_text(json.dumps(error_message))
                    
            except WebSocketDisconnect:
                logger.info(f"WebSocket disconnected: obj_type={obj_type}, user_id={user_id}")
                break
            except Exception as e:
                logger.error(f"Error in websocket loop for user {user_id}: {str(e)}")
                break
                
    except Exception as e:
        logger.error(f"Error in websocket endpoint for user {user_id}: {str(e)}")
    finally:
        # Clean up connection
        try:
            await websocket_manager.disconnect(websocket)
            logger.info(f"WebSocket cleanup completed: obj_type={obj_type}, user_id={user_id}")
        except Exception as cleanup_error:
            logger.error(f"Error during websocket cleanup for user {user_id}: {str(cleanup_error)}")


@router.websocket("/ws/topic/{topic_id}")
async def topic_websocket_endpoint(websocket: WebSocket, topic_id: str):
    """
    WebSocket endpoint for individual topic message updates.
    
    URL pattern: /ws/topic/{topic_id}
    - topic_id: The specific topic ID to subscribe to
    - Authorization: Bearer token in header (required)
    
    """
    

    
    # Authenticate the connection
    user_id = await authenticate_websocket_connection(websocket, "topic", topic_id)
    if user_id is None:
        return
    
    # Subscribe user to topic websocket notifications using unified interface
    success = await topic_websocket_manager.subscribe(topic_id, user_id, websocket)
    
    if not success:
        logger.error(f"Failed to subscribe user {user_id} to topic {topic_id}")
        return
    
    try:
        # Send initial connection confirmation
        welcome_message = {
            "type": "connection_status",
            "topic_id": topic_id,
            "message": f"Successfully connected to topic {topic_id} updates",
            "timestamp": datetime.utcnow().isoformat(),
            "user_id": user_id
        }
        await websocket.send_text(json.dumps(welcome_message))
        
        # Keep connection alive and handle incoming messages
        while True:
            try:
                # Wait for messages from client (optional - can be used for ping/pong)
                data = await websocket.receive_text()
                
                # Parse client message
                try:
                    client_message = json.loads(data)
                    await _handle_topic_client_message(websocket, topic_id, user_id, client_message)
                except json.JSONDecodeError:
                    error_message = {
                        "type": "error",
                        "message": "Invalid JSON format",
                        "timestamp": datetime.utcnow().isoformat()
                    }
                    await websocket.send_text(json.dumps(error_message))
                    
            except WebSocketDisconnect:
                logger.info(f"Topic WebSocket disconnected: topic_id={topic_id}, user_id={user_id}")
                break
            except Exception as e:
                logger.error(f"Error in topic websocket loop for user {user_id}: {str(e)}")
                break
                
    except Exception as e:
        logger.error(f"Error in topic websocket endpoint for user {user_id}: {str(e)}")
    finally:
        # Clean up connection
        try:
            await topic_websocket_manager.disconnect(websocket)
            logger.info(f"Topic WebSocket cleanup completed: topic_id={topic_id}, user_id={user_id}")
        except Exception as cleanup_error:
            logger.error(f"Error during topic websocket cleanup for user {user_id}: {str(cleanup_error)}")


async def _handle_client_message(websocket: WebSocket, obj_type: str, user_id: int, message: Dict[str, Any]):
    """
    Handle messages from the client.
    
    Supported message types:
    - ping: Respond with pong for connection health check
    - status: Send current connection status
    """
    
    message_type = message.get("type", "").lower()
    
    if message_type == "ping":
        pong_message = {
            "type": "pong",
            "obj_type": obj_type,
            "timestamp": datetime.utcnow().isoformat(),
            "user_id": user_id
        }
        await websocket.send_text(json.dumps(pong_message))
        
    elif message_type == "status":
        status_message = {
            "type": "connection_status",
            "obj_type": obj_type,
            "message": f"Connected to {obj_type} updates",
            "timestamp": datetime.utcnow().isoformat(),
            "user_id": user_id,
            "connected_users": len(websocket_manager.get_connected_users(obj_type))
        }
        await websocket.send_text(json.dumps(status_message))
        
    else:
        error_message = {
            "type": "error",
            "message": f"Unknown message type: {message_type}",
            "timestamp": datetime.utcnow().isoformat()
        }
        await websocket.send_text(json.dumps(error_message))


async def _handle_topic_client_message(websocket: WebSocket, topic_id: str, user_id: int, message: Dict[str, Any]):
    """
    Handle messages from the topic client.
    
    Supported message types:
    - ping: Respond with pong for connection health check
    - status: Send current connection status
    """
    
    message_type = message.get("type", "").lower()
    
    if message_type == "ping":
        pong_message = {
            "type": "pong",
            "topic_id": topic_id,
            "timestamp": datetime.utcnow().isoformat(),
            "user_id": user_id
        }
        await websocket.send_text(json.dumps(pong_message))
        
    elif message_type == "status":
        status_message = {
            "type": "connection_status",
            "topic_id": topic_id,
            "message": f"Connected to topic {topic_id} updates",
            "timestamp": datetime.utcnow().isoformat(),
            "user_id": user_id,
            "connected_users": len(topic_websocket_manager.get_connected_users(topic_id))
        }
        await websocket.send_text(json.dumps(status_message))
        
    else:
        error_message = {
            "type": "error",
            "message": f"Unknown message type: {message_type}",
            "timestamp": datetime.utcnow().isoformat()
        }
        await websocket.send_text(json.dumps(error_message))


@router.get("/ws/stats", response_model=AllWebSocketStats)
async def get_websocket_stats():
    """
    Get comprehensive WebSocket connection statistics for both object types and topics.
    
    Returns detailed connection information including:
    - Summary statistics for all connections
    - Detailed connection information for each object type
    - Detailed connection information for each topic
    - Individual connection details with user IDs and connection IDs
    """
    try:
        # Get detailed stats for both object types and topics
        object_type_stats = websocket_manager.get_detailed_connection_stats()
        topic_stats = topic_websocket_manager.get_detailed_connection_stats()
        
        # Combine summary statistics
        combined_summary = {
            "total_object_type_connections": object_type_stats.get("summary", {}).get("total_connections", 0),
            "total_topic_connections": topic_stats.get("summary", {}).get("total_connections", 0),
            "total_object_type_users": object_type_stats.get("summary", {}).get("total_unique_users", 0),
            "total_topic_users": topic_stats.get("summary", {}).get("total_unique_users", 0),
            "total_active_websockets": (
                object_type_stats.get("summary", {}).get("total_active_websockets", 0) +
                topic_stats.get("summary", {}).get("total_active_websockets", 0)
            )
        }
        
        return AllWebSocketStats(
            object_types=object_type_stats.get("object_types", {}),
            topics=topic_stats.get("topics", {}),
            summary=combined_summary,
            timestamp=datetime.utcnow().isoformat()
        )
        
    except Exception as e:
        logger.error(f"Error getting websocket stats: {str(e)}")
        raise HTTPException(status_code=500, detail="Error retrieving websocket statistics")


@router.get("/ws/connections/{obj_type}", response_model=WebSocketDetailedStats)
async def get_connected_users(obj_type: str = Path(..., description="Object type")):
    """
    Get detailed connection information for a specific object type.
    
    Args:
        obj_type: Object type (any text)
        
    Returns:
        Detailed connection information including summary and individual connections
    """
    try:
        # Get detailed stats for the specific object type
        detailed_stats = websocket_manager.get_detailed_connection_stats(obj_type)
        
        return WebSocketDetailedStats(
            summary=detailed_stats["summary"],
            connections=detailed_stats["connections"],
            timestamp=detailed_stats["timestamp"]
        )
        
    except Exception as e:
        logger.error(f"Error getting connected users for {obj_type}: {str(e)}")
        raise HTTPException(status_code=500, detail="Error retrieving connected users")





@router.get("/ws/topic/connections/{topic_id}", response_model=TopicWebSocketDetailedStats)
async def get_topic_connected_users(topic_id: str = Path(..., description="Topic ID")):
    """
    Get detailed connection information for a specific topic.
    
    Args:
        topic_id: Topic ID
        
    Returns:
        Detailed connection information including summary and individual connections
    """
    try:
        # Get detailed stats for the specific topic
        detailed_stats = topic_websocket_manager.get_detailed_connection_stats(topic_id)
        
        return TopicWebSocketDetailedStats(
            summary=detailed_stats["summary"],
            connections=detailed_stats["connections"],
            timestamp=detailed_stats["timestamp"]
        )
        
    except Exception as e:
        logger.error(f"Error getting connected users for topic {topic_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Error retrieving topic connected users")





@router.get("/ws/topic/stats")
async def get_topic_websocket_stats():
    """
    Get detailed topic WebSocket connection statistics.
    
    Returns detailed connection information for all topics including:
    - Summary statistics for all topic connections
    - Detailed connection information for each topic
    - Individual connection details with user IDs and connection IDs
    """
    try:
        detailed_stats = topic_websocket_manager.get_detailed_connection_stats()
        
        return {
            "topics": detailed_stats.get("topics", {}),
            "summary": detailed_stats.get("summary", {}),
            "timestamp": detailed_stats.get("timestamp", datetime.utcnow().isoformat())
        }
        
    except Exception as e:
        logger.error(f"Error getting topic websocket stats: {str(e)}")
        raise HTTPException(status_code=500, detail="Error retrieving topic websocket statistics")



