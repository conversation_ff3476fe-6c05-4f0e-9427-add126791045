from typing import List, Optional
from pydantic import BaseModel, Field, field_validator
from enum import Enum
from exceptions.exceptions import CommonException


class ChatPriorityEnum(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"


class ChatStatusEnum(str, Enum):
    OPEN = "open"
    RESOLVED = "resolved"
    CLOSED = "closed"
    ARCHIVED = "archived"


class ChatAppCodeEnum(str, Enum):
    PROMOSMART = "promosmart"
    MARKDOWN = "markdown"
    BASESMART = "basesmart"


class ChatTopicCreate(BaseModel):
    name: str
    description: Optional[str] = None
    object_type: str
    object_ids: List[str] = Field(default_factory=list)
    app_code: Optional[ChatAppCodeEnum] = None

    @field_validator("name")
    @classmethod
    def validate_name(cls, value):
        if not value.strip():
            raise CommonException("Chat topic name cannot be empty")
        return value.strip()

    @field_validator("object_ids")
    @classmethod
    def validate_object_ids(cls, value):
        if not value:
            raise CommonException("At least one object_id is required")
        return value


class ChatTopicUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    status: Optional[ChatStatusEnum] = None
    app_code: Optional[ChatAppCodeEnum] = None


class ChatMessageCreate(BaseModel):
    topic_id: str
    content: str
    tagged_users: Optional[List[int]] = Field(default_factory=list)
    priority: ChatPriorityEnum = ChatPriorityEnum.LOW
    reply_to: Optional[str] = None

    @field_validator("content")
    @classmethod
    def validate_content(cls, value):
        if not value.strip():
            raise CommonException("Message content cannot be empty")
        return value.strip()


class ChatMessageUpdate(BaseModel):
    content: str
    is_pinned: Optional[bool] = None

    @field_validator("content")
    @classmethod
    def validate_content(cls, value):
        if not value.strip():
            raise CommonException("Message content cannot be empty")
        return value.strip()


class ChatPinTopicRequest(BaseModel):
    is_pinned: bool = Field(..., description="Whether to pin or unpin the topic")


class ChatAddMembersRequest(BaseModel):
    user_ids: List[int] = Field(..., min_items=1)

    @field_validator("user_ids")
    @classmethod
    def validate_user_ids(cls, value):
        if not value:
            raise CommonException("At least one user_id is required")
        return value


class ChatRemoveMembersRequest(BaseModel):
    user_ids: List[int] = Field(..., min_items=1)

    @field_validator("user_ids")
    @classmethod
    def validate_user_ids(cls, value):
        if not value:
            raise CommonException("At least one user_id is required")
        return value


class ChatTopicFilter(BaseModel):
    object_type: str
    object_ids: Optional[List[str]] = None


class ChatUnreadFilter(BaseModel):
    user_id: int
    topic_id: Optional[str] = None
    object_type: Optional[str] = None
    object_id: Optional[str] = None
