import configparser
import datetime
import json
import os
import sys
from io import BytesIO
from typing import Any, Dict, List, Optional, Tuple, Union
from numbers import Number
import aiopg
from common.email_service import EmailService
from common.secret_manager_async_client import AsyncSecretManagerClientSettings
from configuration.environment import environment
from pricesmart_common.constants import CRON_EMAIL_LIST
from pricesmart_common import models as common_models
from fastapi import status as http_status
from starlette.datastructures import UploadFile as StarletteUploadFile
from pricesmart.database import database_constants
from pricesmart_common import constants as common_constants
from py_linq import Enumerable


def get_env_config_value() -> <PERSON><PERSON>[str, configparser.RawConfigParser]:
    config: configparser.RawConfigParser = configparser.RawConfigParser()
    try:
        environment = os.environ["env"]
    except KeyError:
        print("Error: 'env' environment variable not set.")
        sys.exit("Error with reading env. Exiting code..!!")

    # to get to environment.cfg file when running from debugger
    if "venv" in os.getcwd():
        os.chdir("../../")

    config_file_path = os.path.join(os.getcwd(), "environment.conf")
    if not os.path.exists(config_file_path):
        print(f"Error: Config file not found at {config_file_path}")
        sys.exit("Error with reading config file. Exiting code..!!")

    config.read(config_file_path)
    config_dict = {
        section: dict(config.items(section)) for section in config.sections()
    }
    return environment, config_dict


def format_errors(raw_errors: List[Dict[str, Any]]):
    formatted_errors = []
    for err in raw_errors:
        loc = err["loc"]
        param = ""
        if len(loc) > 1:
            loc = loc[1:]
        for val in loc:
            if isinstance(val, int):
                curr_val = f"[{val}]"
                param = param.rstrip(".")
            else:
                curr_val = val
            param = f"{param}{curr_val}."
        param = param.rstrip(".")
        err_msg = err["msg"]
        formatted_errors.append({"param": param, "msg": err_msg})
    return formatted_errors


def get_array_repr(val):
    if isinstance(val, list) or isinstance(val, set):
        return "{" + ",".join(repr(v) for v in val) + "}"
    else:
        return val
    
def get_array_format(val:Optional[list])->str:
    if val is None:
        return "null"

    return f"array[{','.join([get_str_repr(v) for v in val])}]"

def get_json_format(val)-> str:
    if val is None:
        return "null"
    if isinstance(val,dict):
        return get_str_repr(val)
    elif isinstance(val,list):
        return get_str_repr(json.dumps(val))
    
    return get_str_repr(val)

async def async_execute_query(query, transaction_mode=False, timelimit: int|None=300)-> list:
    async with aiopg.connect(dsn=environment.conn_string, timeout=timelimit) as conn:
        async with conn.cursor() as cursor:
            if transaction_mode:
                await cursor.execute(database_constants.BEGIN_TRANSACTION)
                await cursor.execute(query)
                result = await fetchallasync(cursor) 
                await cursor.execute(database_constants.END_TRANSACTION)
                return result
            else:
                await cursor.execute(query)
                return await fetchallasync(cursor)

async def fetchallasync(cursor):
    if not cursor.description:
        return []

    enumerable = Enumerable(
        await cursor.fetchall()
    )

    columns = [col[0] for col in cursor.description]
    return [dict(zip(columns, row)) for row in enumerable]


def get_str_repr(val):
    val = escape_quotes(val)
    if val is None:
        return "null"
    elif isinstance(val, bool):
        return str(val).lower()
    elif isinstance(val, str):
        return f"'{val.strip()}'"
    elif isinstance(val, list) or isinstance(val, set) or isinstance(val, tuple):
        return "(" + ",".join(get_str_repr(v) for v in val) + ")"
    elif isinstance(val, datetime.datetime) or isinstance(val, datetime.date):
        return f"'{str(val)}'"
    elif isinstance(val, dict):
        return f"'{json.dumps(val, default=str)}'"
    else:
        return str(val)


def get_key_value_str(key, val, negate=False):
    if isinstance(val, list) or isinstance(val, set):
        if not val:
            return ""
        val = list(map(get_str_repr, val))
        val_str = ", ".join(val)
        if negate:
            return f"{key} not in ({val_str})"
        else:
            return f"{key} in ({val_str})"
    if isinstance(val, str) and not val:
        return ""
    val_str = get_str_repr(val)
    if negate:
        return f"{key} != {val_str}"
    else:
        return f"{key} = {val_str}"

def create_response(message=common_constants.SUCCESS_MESSAGE, status = http_status.HTTP_200_OK, user_id = None, data = None):
    return common_models.BaseResponseBody(
        message=message, status=status, user_id=user_id, data=data
    )

def escape_quotes(value):
    if value:
        if isinstance(value, list):
            mod_value = []
            for val in value:
                if val or val == 0:
                    mod_value.append(
                        val.replace("'", "''") if isinstance(val, str) else val
                    )
                else:
                    mod_value.append(None)
            return mod_value
        elif isinstance(value, str):
            return value.replace("'", "''")
    return value


async def fetch_email_config(
    secret_id="notification_email_credentials", version_id="latest"
):
    secret_client = AsyncSecretManagerClientSettings()
    conf = await secret_client.fetch_secret(
        secret_id=secret_id,
        version_id=version_id,
        project_id=environment.project_id,
    )
    return conf


# async def send_email(subject, message, subtype="html", attachments=None):
#     conf = await fetch_email_config()
#     email_service = EmailService(conf=EmailService.get_email_conf(**conf))
#     emails = [
#         {
#             "subject": subject,
#             "recipients": CRON_EMAIL_LIST,
#             "body": message,
#             "subtype": subtype
#         }
#     ]
#     await email_service.send_multiple_emails(emails)


async def send_email(subject, message, subtype="html", attachments=None):
    conf = await fetch_email_config()
    email_service = EmailService(conf=EmailService.get_email_conf(**conf))

    # If attachments are None, initialize it to an empty list
    if attachments is None:
        attachments = []

    emails = [
        {
            "subject": subject,
            "recipients": CRON_EMAIL_LIST,
            "body": message,
            "subtype": subtype,
            "attachments": attachments,
        }
    ]
    await email_service.send_multiple_emails(emails)


def create_upload_file(file_name: str, file_content: bytes) -> StarletteUploadFile:
    """
    Create an UploadFile instance from a file name and content.
    """
    file_like = BytesIO(file_content)
    file_like.name = file_name  # Set the name attribute for identification
    return StarletteUploadFile(file=file_like, filename=file_name)



def convert_to_type(value, type_name):
    if type_name == "integer":
        return int(value)
    elif type_name == "numeric":
        return float(value)
    elif type_name == "boolean":
        return value.lower() == "true"
    elif type_name == "date":
        return datetime.datetime.strptime(value, "%Y-%m-%d")
    elif type_name == "timestamp":
        return datetime.datetime.strptime(value, "%Y-%m-%d %H:%M:%S")
    elif type_name == "text":
        return str(value)
    elif type_name == "json":
        return json.loads(value)
    else:
        return value

def generate_filter_condition(
    column_name: str, operator: str, **kwargs
) -> str:
    if kwargs.get("value"):
        return generate_filter_condition_for_string(
            column_name, operator, kwargs["value"]
            )
    
    if isinstance(kwargs.get("value1"),Number):
        return generate_filter_condition_for_numeric_or_date(
            column_name, operator, kwargs["value1"], kwargs.get("value2")
        )
    if isinstance(kwargs.get("value1"),datetime.date) or isinstance(kwargs.get("value1"),str):
        return generate_filter_condition_for_numeric_or_date(
            column_name, operator, kwargs["value1"], kwargs.get("value2")
        )
    
    return ""

def generate_filter_condition_for_string(
    column_name: str, comparision_operator: str, value: str
) -> str:
    condition_string = ""
    if comparision_operator == "contains":
        condition_string = f" {column_name} like {get_str_repr(f'%{value}%')} "
    elif comparision_operator == "not_contains":
        condition_string = f" {column_name} not like {get_str_repr(f'%{value}%')} "
    elif comparision_operator == "equals":
        condition_string = f" {column_name} = {get_str_repr(value)} "
    elif comparision_operator == "not_equals":
        condition_string = f" {column_name} != {get_str_repr(value)} "
    elif comparision_operator == "starts_with":
        condition_string = f" {column_name} like {get_str_repr(f'{value}%')} "
    elif comparision_operator == "ends_with":
        condition_string = f" {column_name} like {get_str_repr(f'%{value}')} "

    return condition_string

def generate_filter_condition_for_numeric_or_date(
    column_name: str,
    comparision_operator: str,
    value1: Union[str,Number,datetime.date],
    value2: Union[str,Number,datetime.date] | None = None
) -> str:
    condition_string = ""
    if comparision_operator == "greater_than":
        condition_string = f" {column_name} > {value1} "
    elif comparision_operator == "greater_than_or_equals":
        condition_string = f" {column_name} >= {value1} "
    elif comparision_operator == "less_than":
        condition_string = f" {column_name} < {value1} "
    elif comparision_operator == "less_than_or_equals":
        condition_string = f" {column_name} <= {value1} "
    elif comparision_operator == "between":
        condition_string = f" {column_name} between {value1} and {value2} "
    elif comparision_operator == "equals":
        condition_string = f" {column_name} = {value1} "
    elif comparision_operator == "not_equals":
        condition_string = f" {column_name} != {value1} "
    return condition_string